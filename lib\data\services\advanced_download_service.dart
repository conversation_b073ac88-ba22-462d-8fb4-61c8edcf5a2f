import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:workmanager/workmanager.dart';

import '../../core/errors/exceptions.dart';
import '../../domain/entities/download_item.dart';
import '../../domain/entities/video_info.dart';
import '../../domain/entities/audio_info.dart';

/// 🚀 Advanced Download Service
/// Professional download management with background processing, queue management, and smart retry
@LazySingleton()
class AdvancedDownloadService {
  final Dio _dio;
  final FlutterLocalNotificationsPlugin _notifications;
  
  final Map<String, DownloadTask> _activeTasks = {};
  final Map<String, CancelToken> _cancelTokens = {};
  final List<DownloadItem> _downloadQueue = [];
  final StreamController<DownloadEvent> _eventController = StreamController.broadcast();
  
  static const int _maxConcurrentDownloads = 3;
  static const int _maxRetryAttempts = 3;
  static const Duration _retryDelay = Duration(seconds: 5);

  AdvancedDownloadService(this._dio, this._notifications) {
    _initializeNotifications();
    _initializeWorkManager();
  }

  /// Stream of download events
  Stream<DownloadEvent> get eventStream => _eventController.stream;

  /// Get active downloads count
  int get activeDownloadsCount => _activeTasks.length;

  /// Get queue length
  int get queueLength => _downloadQueue.length;

  /// Check if download limit is reached
  bool get isDownloadLimitReached => _activeTasks.length >= _maxConcurrentDownloads;

  /// Initialize notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings();
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );
    
    await _notifications.initialize(initSettings);
    
    // Create notification channels
    const androidChannel = AndroidNotificationChannel(
      'download_channel',
      'Download Progress',
      description: 'Shows download progress notifications',
      importance: Importance.low,
      showBadge: false,
    );
    
    await _notifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(androidChannel);
  }

  /// Initialize WorkManager for background downloads
  void _initializeWorkManager() {
    Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: false,
    );
  }

  /// Add video download to queue
  Future<String> queueVideoDownload({
    required VideoInfo videoInfo,
    required VideoQuality quality,
    String? customPath,
    Map<String, String>? headers,
    DownloadPriority priority = DownloadPriority.normal,
    bool enableWatermarkRemoval = false,
  }) async {
    final downloadId = 'video_${DateTime.now().millisecondsSinceEpoch}';
    final fileName = _generateFileName(videoInfo.title, quality.quality, quality.format);
    final filePath = await _getDownloadPath(fileName, customPath, isVideo: true);

    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: videoInfo.id,
      title: videoInfo.title,
      url: videoInfo.url,
      downloadUrl: quality.downloadUrl,
      thumbnailUrl: videoInfo.thumbnailUrl ?? '',
      platform: videoInfo.platform,
      quality: quality.quality,
      format: quality.format,
      filePath: filePath,
      status: DownloadStatus.pending,
      progress: 0.0,
      createdAt: DateTime.now(),
      totalBytes: quality.fileSize,
      metadata: {
        'priority': priority.index,
        'enableWatermarkRemoval': enableWatermarkRemoval,
        'headers': headers,
      },
    );

    _addToQueue(downloadItem, priority);
    _processQueue();
    
    return downloadId;
  }

  /// Add audio download to queue
  Future<String> queueAudioDownload({
    required AudioInfo audioInfo,
    required AudioFormat format,
    String? customPath,
    Map<String, String>? headers,
    DownloadPriority priority = DownloadPriority.normal,
  }) async {
    final downloadId = 'audio_${DateTime.now().millisecondsSinceEpoch}';
    final fileName = _generateFileName(audioInfo.title, '${format.bitrate}kbps', format.format);
    final filePath = await _getDownloadPath(fileName, customPath, isVideo: false);

    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: audioInfo.id,
      title: audioInfo.title,
      url: audioInfo.sourceUrl,
      downloadUrl: format.downloadUrl,
      thumbnailUrl: audioInfo.thumbnailUrl ?? '',
      platform: audioInfo.platform,
      quality: '${format.bitrate}kbps',
      format: format.format,
      filePath: filePath,
      status: DownloadStatus.pending,
      progress: 0.0,
      createdAt: DateTime.now(),
      totalBytes: format.fileSize,
      metadata: {
        'priority': priority.index,
        'headers': headers,
        'isAudio': true,
      },
    );

    _addToQueue(downloadItem, priority);
    _processQueue();
    
    return downloadId;
  }

  /// Add item to queue with priority
  void _addToQueue(DownloadItem item, DownloadPriority priority) {
    switch (priority) {
      case DownloadPriority.high:
        _downloadQueue.insert(0, item);
        break;
      case DownloadPriority.normal:
        _downloadQueue.add(item);
        break;
      case DownloadPriority.low:
        _downloadQueue.add(item);
        break;
    }
    
    _emitEvent(DownloadQueueUpdated(_downloadQueue.length));
  }

  /// Process download queue
  Future<void> _processQueue() async {
    while (_downloadQueue.isNotEmpty && !isDownloadLimitReached) {
      final item = _downloadQueue.removeAt(0);
      await _startDownload(item);
    }
  }

  /// Start individual download
  Future<void> _startDownload(DownloadItem item) async {
    final cancelToken = CancelToken();
    _cancelTokens[item.id] = cancelToken;

    final task = DownloadTask(
      item: item,
      cancelToken: cancelToken,
      startTime: DateTime.now(),
      retryCount: 0,
    );

    _activeTasks[item.id] = task;
    _emitEvent(DownloadStarted(item));
    
    await _showDownloadNotification(item);

    try {
      await _performDownload(task);
    } catch (e) {
      await _handleDownloadError(task, e);
    }
  }

  /// Perform the actual download with advanced features
  Future<void> _performDownload(DownloadTask task) async {
    final item = task.item;
    final file = File(item.filePath);
    await file.parent.create(recursive: true);

    // Check for existing partial download
    int downloadedBytes = 0;
    if (await file.exists()) {
      downloadedBytes = await file.length();
      if (downloadedBytes == item.totalBytes) {
        await _completeDownload(task);
        return;
      }
    }

    // Prepare headers
    final headers = <String, dynamic>{
      ...?item.metadata?['headers'] as Map<String, String>?,
      if (downloadedBytes > 0) 'Range': 'bytes=$downloadedBytes-',
    };

    final options = Options(
      headers: headers,
      responseType: ResponseType.stream,
    );

    _emitEvent(DownloadProgressUpdated(item.id, DownloadStatus.downloading, 0.0));

    final response = await _dio.get(
      item.downloadUrl,
      options: options,
      cancelToken: task.cancelToken,
      onReceiveProgress: (received, total) {
        final progress = total > 0 ? (received + downloadedBytes) / item.totalBytes! : 0.0;
        final speed = _calculateSpeed(received, task.startTime);
        final eta = _calculateETA(received + downloadedBytes, item.totalBytes!, speed);
        
        _emitEvent(DownloadProgressUpdated(
          item.id,
          DownloadStatus.downloading,
          progress,
          downloadSpeed: speed,
          eta: eta,
          downloadedBytes: received + downloadedBytes,
        ));
        
        _updateDownloadNotification(item, progress);
      },
    );

    // Save file
    final sink = file.openWrite(mode: downloadedBytes > 0 ? FileMode.append : FileMode.write);
    await response.data.stream.pipe(sink);
    await sink.close();

    // Post-processing
    await _postProcessDownload(task);
    await _completeDownload(task);
  }

  /// Post-process download (watermark removal, format conversion, etc.)
  Future<void> _postProcessDownload(DownloadTask task) async {
    final item = task.item;
    final enableWatermarkRemoval = item.metadata?['enableWatermarkRemoval'] as bool? ?? false;
    
    if (enableWatermarkRemoval && !item.metadata?['isAudio'] == true) {
      _emitEvent(DownloadProgressUpdated(
        item.id,
        DownloadStatus.downloading,
        0.9,
        message: 'Removing watermark...',
      ));
      
      // Implement watermark removal here
      await _removeWatermark(item.filePath);
    }
  }

  /// Remove watermark from video
  Future<void> _removeWatermark(String filePath) async {
    // This would integrate with the WatermarkRemovalService
    // For now, just simulate the process
    await Future.delayed(const Duration(seconds: 2));
  }

  /// Complete download
  Future<void> _completeDownload(DownloadTask task) async {
    final item = task.item.copyWith(
      status: DownloadStatus.completed,
      progress: 1.0,
      completedAt: DateTime.now(),
    );

    _activeTasks.remove(item.id);
    _cancelTokens.remove(item.id);
    
    _emitEvent(DownloadCompleted(item));
    await _showCompletionNotification(item);
    
    // Process next item in queue
    _processQueue();
  }

  /// Handle download errors with smart retry
  Future<void> _handleDownloadError(DownloadTask task, dynamic error) async {
    final item = task.item;
    
    if (task.retryCount < _maxRetryAttempts && !task.cancelToken.isCancelled) {
      // Retry download
      task.retryCount++;
      _emitEvent(DownloadRetrying(item, task.retryCount));
      
      await Future.delayed(_retryDelay);
      
      try {
        await _performDownload(task);
        return;
      } catch (e) {
        await _handleDownloadError(task, e);
        return;
      }
    }

    // Mark as failed
    String errorMessage = 'Download failed';
    if (error is DioException) {
      errorMessage = _getDioErrorMessage(error);
    } else if (error is AppException) {
      errorMessage = error.message;
    }

    final failedItem = item.copyWith(
      status: DownloadStatus.failed,
      errorMessage: errorMessage,
      updatedAt: DateTime.now(),
    );

    _activeTasks.remove(item.id);
    _cancelTokens.remove(item.id);
    
    _emitEvent(DownloadFailed(failedItem, errorMessage));
    await _showErrorNotification(failedItem, errorMessage);
    
    // Process next item in queue
    _processQueue();
  }

  /// Pause download
  Future<void> pauseDownload(String downloadId) async {
    final task = _activeTasks[downloadId];
    if (task != null) {
      task.cancelToken.cancel('Paused by user');
      
      final pausedItem = task.item.copyWith(
        status: DownloadStatus.paused,
        updatedAt: DateTime.now(),
      );
      
      _emitEvent(DownloadPaused(pausedItem));
    }
  }

  /// Resume download
  Future<void> resumeDownload(DownloadItem item) async {
    if (!_activeTasks.containsKey(item.id)) {
      final resumedItem = item.copyWith(
        status: DownloadStatus.pending,
        startedAt: DateTime.now(),
      );
      
      _addToQueue(resumedItem, DownloadPriority.high);
      _processQueue();
    }
  }

  /// Cancel download
  Future<void> cancelDownload(String downloadId) async {
    final task = _activeTasks[downloadId];
    if (task != null) {
      task.cancelToken.cancel('Cancelled by user');
      
      final cancelledItem = task.item.copyWith(
        status: DownloadStatus.cancelled,
        updatedAt: DateTime.now(),
      );
      
      _activeTasks.remove(downloadId);
      _cancelTokens.remove(downloadId);
      
      _emitEvent(DownloadCancelled(cancelledItem));
      await _cancelDownloadNotification(downloadId);
      
      // Process next item in queue
      _processQueue();
    }
    
    // Remove from queue if pending
    _downloadQueue.removeWhere((item) => item.id == downloadId);
  }

  /// Cancel all downloads
  Future<void> cancelAllDownloads() async {
    final downloadIds = List<String>.from(_activeTasks.keys);
    
    for (final id in downloadIds) {
      await cancelDownload(id);
    }
    
    _downloadQueue.clear();
    _emitEvent(DownloadQueueUpdated(0));
  }

  /// Schedule download for later
  Future<void> scheduleDownload(DownloadItem item, DateTime scheduledTime) async {
    final delay = scheduledTime.difference(DateTime.now());
    
    if (delay.isNegative) {
      throw ValidationException(message: 'Scheduled time must be in the future');
    }
    
    await Workmanager().registerOneOffTask(
      'scheduled_download_${item.id}',
      'scheduledDownload',
      initialDelay: delay,
      inputData: {
        'downloadId': item.id,
        'downloadData': item.toJson(),
      },
    );
    
    _emitEvent(DownloadScheduled(item, scheduledTime));
  }

  /// Notification methods
  Future<void> _showDownloadNotification(DownloadItem item) async {
    await _notifications.show(
      item.id.hashCode,
      'Downloading ${item.title}',
      'Starting download...',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'download_channel',
          'Download Progress',
          channelDescription: 'Shows download progress notifications',
          importance: Importance.low,
          priority: Priority.low,
          showProgress: true,
          maxProgress: 100,
          progress: 0,
          ongoing: true,
        ),
      ),
    );
  }

  Future<void> _updateDownloadNotification(DownloadItem item, double progress) async {
    await _notifications.show(
      item.id.hashCode,
      'Downloading ${item.title}',
      '${(progress * 100).toStringAsFixed(1)}% completed',
      NotificationDetails(
        android: AndroidNotificationDetails(
          'download_channel',
          'Download Progress',
          channelDescription: 'Shows download progress notifications',
          importance: Importance.low,
          priority: Priority.low,
          showProgress: true,
          maxProgress: 100,
          progress: (progress * 100).round(),
          ongoing: true,
        ),
      ),
    );
  }

  Future<void> _showCompletionNotification(DownloadItem item) async {
    await _notifications.show(
      item.id.hashCode,
      'Download Complete',
      '${item.title} has been downloaded successfully',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'download_channel',
          'Download Progress',
          channelDescription: 'Shows download progress notifications',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
      ),
    );
  }

  Future<void> _showErrorNotification(DownloadItem item, String error) async {
    await _notifications.show(
      item.id.hashCode,
      'Download Failed',
      '${item.title}: $error',
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'download_channel',
          'Download Progress',
          channelDescription: 'Shows download progress notifications',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
        ),
      ),
    );
  }

  Future<void> _cancelDownloadNotification(String downloadId) async {
    await _notifications.cancel(downloadId.hashCode);
  }

  /// Utility methods
  String _generateFileName(String title, String quality, String format) {
    final cleanTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();
    final truncatedTitle = cleanTitle.length > 100 ? cleanTitle.substring(0, 100) : cleanTitle;
    return '${truncatedTitle}_${quality}.$format';
  }

  Future<String> _getDownloadPath(String fileName, String? customPath, {required bool isVideo}) async {
    String basePath;
    
    if (customPath != null) {
      basePath = customPath;
    } else {
      final directory = await getExternalStorageDirectory();
      basePath = '${directory!.path}/VideoDownloader/${isVideo ? 'Videos' : 'Audio'}';
    }

    return '$basePath/$fileName';
  }

  double _calculateSpeed(int downloadedBytes, DateTime startTime) {
    final elapsed = DateTime.now().difference(startTime).inMilliseconds;
    if (elapsed <= 0) return 0.0;
    return (downloadedBytes * 1000) / elapsed;
  }

  Duration? _calculateETA(int downloadedBytes, int totalBytes, double speed) {
    if (speed <= 0 || totalBytes <= 0 || downloadedBytes >= totalBytes) return null;
    final remainingBytes = totalBytes - downloadedBytes;
    return Duration(seconds: (remainingBytes / speed).round());
  }

  String _getDioErrorMessage(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout';
      case DioExceptionType.receiveTimeout:
        return 'Receive timeout';
      case DioExceptionType.sendTimeout:
        return 'Send timeout';
      case DioExceptionType.cancel:
        return 'Download cancelled';
      case DioExceptionType.connectionError:
        return 'Connection error';
      case DioExceptionType.badResponse:
        return 'Server error: ${error.response?.statusCode}';
      default:
        return 'Network error';
    }
  }

  void _emitEvent(DownloadEvent event) {
    _eventController.add(event);
  }

  /// Dispose resources
  void dispose() {
    cancelAllDownloads();
    _eventController.close();
  }
}

/// 📋 Download Task
class DownloadTask {
  final DownloadItem item;
  final CancelToken cancelToken;
  final DateTime startTime;
  int retryCount;

  DownloadTask({
    required this.item,
    required this.cancelToken,
    required this.startTime,
    this.retryCount = 0,
  });
}

/// 🎯 Download Priority
enum DownloadPriority { high, normal, low }

/// 📡 Download Events
abstract class DownloadEvent {}

class DownloadStarted extends DownloadEvent {
  final DownloadItem item;
  DownloadStarted(this.item);
}

class DownloadProgressUpdated extends DownloadEvent {
  final String downloadId;
  final DownloadStatus status;
  final double progress;
  final double? downloadSpeed;
  final Duration? eta;
  final int? downloadedBytes;
  final String? message;

  DownloadProgressUpdated(
    this.downloadId,
    this.status,
    this.progress, {
    this.downloadSpeed,
    this.eta,
    this.downloadedBytes,
    this.message,
  });
}

class DownloadCompleted extends DownloadEvent {
  final DownloadItem item;
  DownloadCompleted(this.item);
}

class DownloadFailed extends DownloadEvent {
  final DownloadItem item;
  final String error;
  DownloadFailed(this.item, this.error);
}

class DownloadPaused extends DownloadEvent {
  final DownloadItem item;
  DownloadPaused(this.item);
}

class DownloadCancelled extends DownloadEvent {
  final DownloadItem item;
  DownloadCancelled(this.item);
}

class DownloadRetrying extends DownloadEvent {
  final DownloadItem item;
  final int retryCount;
  DownloadRetrying(this.item, this.retryCount);
}

class DownloadScheduled extends DownloadEvent {
  final DownloadItem item;
  final DateTime scheduledTime;
  DownloadScheduled(this.item, this.scheduledTime);
}

class DownloadQueueUpdated extends DownloadEvent {
  final int queueLength;
  DownloadQueueUpdated(this.queueLength);
}

/// 🔄 Background task callback
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case 'scheduledDownload':
        // Handle scheduled download
        final downloadId = inputData?['downloadId'] as String?;
        if (downloadId != null) {
          // Resume the scheduled download
          // This would integrate with the main app
        }
        break;
    }
    return Future.value(true);
  });
}
