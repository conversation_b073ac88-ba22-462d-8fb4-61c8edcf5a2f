// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'download_item_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DownloadItemModel _$DownloadItemModelFromJson(Map<String, dynamic> json) {
  return _DownloadItemModel.fromJson(json);
}

/// @nodoc
mixin _$DownloadItemModel {
  @HiveField(0)
  String get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String get videoId => throw _privateConstructorUsedError;
  @HiveField(2)
  String get title => throw _privateConstructorUsedError;
  @HiveField(3)
  String get url => throw _privateConstructorUsedError;
  @HiveField(4)
  String get downloadUrl => throw _privateConstructorUsedError;
  @HiveField(5)
  String get thumbnailUrl => throw _privateConstructorUsedError;
  @HiveField(6)
  String get platform => throw _privateConstructorUsedError;
  @HiveField(7)
  String get quality => throw _privateConstructorUsedError;
  @HiveField(8)
  String get format => throw _privateConstructorUsedError;
  @HiveField(9)
  String get filePath => throw _privateConstructorUsedError;
  @HiveField(10)
  String get status => throw _privateConstructorUsedError;
  @HiveField(11)
  double get progress => throw _privateConstructorUsedError;
  @HiveField(12)
  DateTime get createdAt => throw _privateConstructorUsedError;
  @HiveField(13)
  DateTime? get startedAt => throw _privateConstructorUsedError;
  @HiveField(14)
  DateTime? get completedAt => throw _privateConstructorUsedError;
  @HiveField(15)
  int? get totalBytes => throw _privateConstructorUsedError;
  @HiveField(16)
  int? get downloadedBytes => throw _privateConstructorUsedError;
  @HiveField(17)
  String? get errorMessage => throw _privateConstructorUsedError;
  @HiveField(18)
  int? get retryCount => throw _privateConstructorUsedError;
  @HiveField(19)
  double? get downloadSpeed => throw _privateConstructorUsedError;
  @HiveField(20)
  Duration? get estimatedTimeRemaining => throw _privateConstructorUsedError;

  /// Serializes this DownloadItemModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DownloadItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DownloadItemModelCopyWith<DownloadItemModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DownloadItemModelCopyWith<$Res> {
  factory $DownloadItemModelCopyWith(
          DownloadItemModel value, $Res Function(DownloadItemModel) then) =
      _$DownloadItemModelCopyWithImpl<$Res, DownloadItemModel>;
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String videoId,
      @HiveField(2) String title,
      @HiveField(3) String url,
      @HiveField(4) String downloadUrl,
      @HiveField(5) String thumbnailUrl,
      @HiveField(6) String platform,
      @HiveField(7) String quality,
      @HiveField(8) String format,
      @HiveField(9) String filePath,
      @HiveField(10) String status,
      @HiveField(11) double progress,
      @HiveField(12) DateTime createdAt,
      @HiveField(13) DateTime? startedAt,
      @HiveField(14) DateTime? completedAt,
      @HiveField(15) int? totalBytes,
      @HiveField(16) int? downloadedBytes,
      @HiveField(17) String? errorMessage,
      @HiveField(18) int? retryCount,
      @HiveField(19) double? downloadSpeed,
      @HiveField(20) Duration? estimatedTimeRemaining});
}

/// @nodoc
class _$DownloadItemModelCopyWithImpl<$Res, $Val extends DownloadItemModel>
    implements $DownloadItemModelCopyWith<$Res> {
  _$DownloadItemModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DownloadItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? videoId = null,
    Object? title = null,
    Object? url = null,
    Object? downloadUrl = null,
    Object? thumbnailUrl = null,
    Object? platform = null,
    Object? quality = null,
    Object? format = null,
    Object? filePath = null,
    Object? status = null,
    Object? progress = null,
    Object? createdAt = null,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? totalBytes = freezed,
    Object? downloadedBytes = freezed,
    Object? errorMessage = freezed,
    Object? retryCount = freezed,
    Object? downloadSpeed = freezed,
    Object? estimatedTimeRemaining = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      videoId: null == videoId
          ? _value.videoId
          : videoId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalBytes: freezed == totalBytes
          ? _value.totalBytes
          : totalBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      downloadedBytes: freezed == downloadedBytes
          ? _value.downloadedBytes
          : downloadedBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      retryCount: freezed == retryCount
          ? _value.retryCount
          : retryCount // ignore: cast_nullable_to_non_nullable
              as int?,
      downloadSpeed: freezed == downloadSpeed
          ? _value.downloadSpeed
          : downloadSpeed // ignore: cast_nullable_to_non_nullable
              as double?,
      estimatedTimeRemaining: freezed == estimatedTimeRemaining
          ? _value.estimatedTimeRemaining
          : estimatedTimeRemaining // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DownloadItemModelImplCopyWith<$Res>
    implements $DownloadItemModelCopyWith<$Res> {
  factory _$$DownloadItemModelImplCopyWith(_$DownloadItemModelImpl value,
          $Res Function(_$DownloadItemModelImpl) then) =
      __$$DownloadItemModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String videoId,
      @HiveField(2) String title,
      @HiveField(3) String url,
      @HiveField(4) String downloadUrl,
      @HiveField(5) String thumbnailUrl,
      @HiveField(6) String platform,
      @HiveField(7) String quality,
      @HiveField(8) String format,
      @HiveField(9) String filePath,
      @HiveField(10) String status,
      @HiveField(11) double progress,
      @HiveField(12) DateTime createdAt,
      @HiveField(13) DateTime? startedAt,
      @HiveField(14) DateTime? completedAt,
      @HiveField(15) int? totalBytes,
      @HiveField(16) int? downloadedBytes,
      @HiveField(17) String? errorMessage,
      @HiveField(18) int? retryCount,
      @HiveField(19) double? downloadSpeed,
      @HiveField(20) Duration? estimatedTimeRemaining});
}

/// @nodoc
class __$$DownloadItemModelImplCopyWithImpl<$Res>
    extends _$DownloadItemModelCopyWithImpl<$Res, _$DownloadItemModelImpl>
    implements _$$DownloadItemModelImplCopyWith<$Res> {
  __$$DownloadItemModelImplCopyWithImpl(_$DownloadItemModelImpl _value,
      $Res Function(_$DownloadItemModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of DownloadItemModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? videoId = null,
    Object? title = null,
    Object? url = null,
    Object? downloadUrl = null,
    Object? thumbnailUrl = null,
    Object? platform = null,
    Object? quality = null,
    Object? format = null,
    Object? filePath = null,
    Object? status = null,
    Object? progress = null,
    Object? createdAt = null,
    Object? startedAt = freezed,
    Object? completedAt = freezed,
    Object? totalBytes = freezed,
    Object? downloadedBytes = freezed,
    Object? errorMessage = freezed,
    Object? retryCount = freezed,
    Object? downloadSpeed = freezed,
    Object? estimatedTimeRemaining = freezed,
  }) {
    return _then(_$DownloadItemModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      videoId: null == videoId
          ? _value.videoId
          : videoId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      filePath: null == filePath
          ? _value.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _value.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      startedAt: freezed == startedAt
          ? _value.startedAt
          : startedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      completedAt: freezed == completedAt
          ? _value.completedAt
          : completedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      totalBytes: freezed == totalBytes
          ? _value.totalBytes
          : totalBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      downloadedBytes: freezed == downloadedBytes
          ? _value.downloadedBytes
          : downloadedBytes // ignore: cast_nullable_to_non_nullable
              as int?,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      retryCount: freezed == retryCount
          ? _value.retryCount
          : retryCount // ignore: cast_nullable_to_non_nullable
              as int?,
      downloadSpeed: freezed == downloadSpeed
          ? _value.downloadSpeed
          : downloadSpeed // ignore: cast_nullable_to_non_nullable
              as double?,
      estimatedTimeRemaining: freezed == estimatedTimeRemaining
          ? _value.estimatedTimeRemaining
          : estimatedTimeRemaining // ignore: cast_nullable_to_non_nullable
              as Duration?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DownloadItemModelImpl implements _DownloadItemModel {
  const _$DownloadItemModelImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) required this.videoId,
      @HiveField(2) required this.title,
      @HiveField(3) required this.url,
      @HiveField(4) required this.downloadUrl,
      @HiveField(5) required this.thumbnailUrl,
      @HiveField(6) required this.platform,
      @HiveField(7) required this.quality,
      @HiveField(8) required this.format,
      @HiveField(9) required this.filePath,
      @HiveField(10) required this.status,
      @HiveField(11) required this.progress,
      @HiveField(12) required this.createdAt,
      @HiveField(13) this.startedAt,
      @HiveField(14) this.completedAt,
      @HiveField(15) this.totalBytes,
      @HiveField(16) this.downloadedBytes,
      @HiveField(17) this.errorMessage,
      @HiveField(18) this.retryCount,
      @HiveField(19) this.downloadSpeed,
      @HiveField(20) this.estimatedTimeRemaining});

  factory _$DownloadItemModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$DownloadItemModelImplFromJson(json);

  @override
  @HiveField(0)
  final String id;
  @override
  @HiveField(1)
  final String videoId;
  @override
  @HiveField(2)
  final String title;
  @override
  @HiveField(3)
  final String url;
  @override
  @HiveField(4)
  final String downloadUrl;
  @override
  @HiveField(5)
  final String thumbnailUrl;
  @override
  @HiveField(6)
  final String platform;
  @override
  @HiveField(7)
  final String quality;
  @override
  @HiveField(8)
  final String format;
  @override
  @HiveField(9)
  final String filePath;
  @override
  @HiveField(10)
  final String status;
  @override
  @HiveField(11)
  final double progress;
  @override
  @HiveField(12)
  final DateTime createdAt;
  @override
  @HiveField(13)
  final DateTime? startedAt;
  @override
  @HiveField(14)
  final DateTime? completedAt;
  @override
  @HiveField(15)
  final int? totalBytes;
  @override
  @HiveField(16)
  final int? downloadedBytes;
  @override
  @HiveField(17)
  final String? errorMessage;
  @override
  @HiveField(18)
  final int? retryCount;
  @override
  @HiveField(19)
  final double? downloadSpeed;
  @override
  @HiveField(20)
  final Duration? estimatedTimeRemaining;

  @override
  String toString() {
    return 'DownloadItemModel(id: $id, videoId: $videoId, title: $title, url: $url, downloadUrl: $downloadUrl, thumbnailUrl: $thumbnailUrl, platform: $platform, quality: $quality, format: $format, filePath: $filePath, status: $status, progress: $progress, createdAt: $createdAt, startedAt: $startedAt, completedAt: $completedAt, totalBytes: $totalBytes, downloadedBytes: $downloadedBytes, errorMessage: $errorMessage, retryCount: $retryCount, downloadSpeed: $downloadSpeed, estimatedTimeRemaining: $estimatedTimeRemaining)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DownloadItemModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.videoId, videoId) || other.videoId == videoId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.downloadUrl, downloadUrl) ||
                other.downloadUrl == downloadUrl) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.startedAt, startedAt) ||
                other.startedAt == startedAt) &&
            (identical(other.completedAt, completedAt) ||
                other.completedAt == completedAt) &&
            (identical(other.totalBytes, totalBytes) ||
                other.totalBytes == totalBytes) &&
            (identical(other.downloadedBytes, downloadedBytes) ||
                other.downloadedBytes == downloadedBytes) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.retryCount, retryCount) ||
                other.retryCount == retryCount) &&
            (identical(other.downloadSpeed, downloadSpeed) ||
                other.downloadSpeed == downloadSpeed) &&
            (identical(other.estimatedTimeRemaining, estimatedTimeRemaining) ||
                other.estimatedTimeRemaining == estimatedTimeRemaining));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        videoId,
        title,
        url,
        downloadUrl,
        thumbnailUrl,
        platform,
        quality,
        format,
        filePath,
        status,
        progress,
        createdAt,
        startedAt,
        completedAt,
        totalBytes,
        downloadedBytes,
        errorMessage,
        retryCount,
        downloadSpeed,
        estimatedTimeRemaining
      ]);

  /// Create a copy of DownloadItemModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DownloadItemModelImplCopyWith<_$DownloadItemModelImpl> get copyWith =>
      __$$DownloadItemModelImplCopyWithImpl<_$DownloadItemModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DownloadItemModelImplToJson(
      this,
    );
  }
}

abstract class _DownloadItemModel implements DownloadItemModel {
  const factory _DownloadItemModel(
          {@HiveField(0) required final String id,
          @HiveField(1) required final String videoId,
          @HiveField(2) required final String title,
          @HiveField(3) required final String url,
          @HiveField(4) required final String downloadUrl,
          @HiveField(5) required final String thumbnailUrl,
          @HiveField(6) required final String platform,
          @HiveField(7) required final String quality,
          @HiveField(8) required final String format,
          @HiveField(9) required final String filePath,
          @HiveField(10) required final String status,
          @HiveField(11) required final double progress,
          @HiveField(12) required final DateTime createdAt,
          @HiveField(13) final DateTime? startedAt,
          @HiveField(14) final DateTime? completedAt,
          @HiveField(15) final int? totalBytes,
          @HiveField(16) final int? downloadedBytes,
          @HiveField(17) final String? errorMessage,
          @HiveField(18) final int? retryCount,
          @HiveField(19) final double? downloadSpeed,
          @HiveField(20) final Duration? estimatedTimeRemaining}) =
      _$DownloadItemModelImpl;

  factory _DownloadItemModel.fromJson(Map<String, dynamic> json) =
      _$DownloadItemModelImpl.fromJson;

  @override
  @HiveField(0)
  String get id;
  @override
  @HiveField(1)
  String get videoId;
  @override
  @HiveField(2)
  String get title;
  @override
  @HiveField(3)
  String get url;
  @override
  @HiveField(4)
  String get downloadUrl;
  @override
  @HiveField(5)
  String get thumbnailUrl;
  @override
  @HiveField(6)
  String get platform;
  @override
  @HiveField(7)
  String get quality;
  @override
  @HiveField(8)
  String get format;
  @override
  @HiveField(9)
  String get filePath;
  @override
  @HiveField(10)
  String get status;
  @override
  @HiveField(11)
  double get progress;
  @override
  @HiveField(12)
  DateTime get createdAt;
  @override
  @HiveField(13)
  DateTime? get startedAt;
  @override
  @HiveField(14)
  DateTime? get completedAt;
  @override
  @HiveField(15)
  int? get totalBytes;
  @override
  @HiveField(16)
  int? get downloadedBytes;
  @override
  @HiveField(17)
  String? get errorMessage;
  @override
  @HiveField(18)
  int? get retryCount;
  @override
  @HiveField(19)
  double? get downloadSpeed;
  @override
  @HiveField(20)
  Duration? get estimatedTimeRemaining;

  /// Create a copy of DownloadItemModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DownloadItemModelImplCopyWith<_$DownloadItemModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
