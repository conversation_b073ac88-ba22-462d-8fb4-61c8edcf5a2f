import 'package:equatable/equatable.dart';

/// 🚨 Base Failure Class
/// All failures in the app should extend this class
abstract class Failure extends Equatable {
  final String message;
  final int? code;
  final dynamic data;

  const Failure({
    required this.message,
    this.code,
    this.data,
  });

  @override
  List<Object?> get props => [message, code, data];
}

/// 🌐 Network Related Failures
class NetworkFailure extends Failure {
  const NetworkFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class NoInternetFailure extends NetworkFailure {
  const NoInternetFailure() : super(message: 'No internet connection available');
}

class ServerFailure extends NetworkFailure {
  const ServerFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class TimeoutFailure extends NetworkFailure {
  const TimeoutFailure() : super(message: 'Request timeout. Please try again.');
}

/// 📱 Platform Related Failures
class PlatformFailure extends Failure {
  const PlatformFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class UnsupportedPlatformFailure extends PlatformFailure {
  const UnsupportedPlatformFailure({required String platform})
      : super(message: 'Platform "$platform" is not supported');
}

class InvalidUrlFailure extends PlatformFailure {
  const InvalidUrlFailure({required String url})
      : super(message: 'Invalid URL: "$url"');
}

class VideoNotFoundFailure extends PlatformFailure {
  const VideoNotFoundFailure()
      : super(message: 'Video not found or may be private/deleted');
}

class VideoUnavailableFailure extends PlatformFailure {
  const VideoUnavailableFailure({required String reason})
      : super(message: 'Video unavailable: $reason');
}

/// 💾 Storage Related Failures
class StorageFailure extends Failure {
  const StorageFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class InsufficientStorageFailure extends StorageFailure {
  const InsufficientStorageFailure()
      : super(message: 'Insufficient storage space');
}

class FileNotFoundFailure extends StorageFailure {
  const FileNotFoundFailure({required String filePath})
      : super(message: 'File not found: $filePath');
}

class FileAccessFailure extends StorageFailure {
  const FileAccessFailure({required String reason})
      : super(message: 'File access denied: $reason');
}

class DirectoryCreationFailure extends StorageFailure {
  const DirectoryCreationFailure({required String path})
      : super(message: 'Failed to create directory: $path');
}

/// 🔐 Permission Related Failures
class PermissionFailure extends Failure {
  const PermissionFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class StoragePermissionFailure extends PermissionFailure {
  const StoragePermissionFailure()
      : super(message: 'Storage permission is required to download files');
}

class NetworkPermissionFailure extends PermissionFailure {
  const NetworkPermissionFailure()
      : super(message: 'Network permission is required to download content');
}

class NotificationPermissionFailure extends PermissionFailure {
  const NotificationPermissionFailure()
      : super(message: 'Notification permission is required for download updates');
}

/// 📥 Download Related Failures
class DownloadFailure extends Failure {
  const DownloadFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class DownloadCancelledFailure extends DownloadFailure {
  const DownloadCancelledFailure()
      : super(message: 'Download was cancelled by user');
}

class DownloadInterruptedFailure extends DownloadFailure {
  const DownloadInterruptedFailure({required String reason})
      : super(message: 'Download interrupted: $reason');
}

class ExtractionFailure extends DownloadFailure {
  const ExtractionFailure({required String reason})
      : super(message: 'Failed to extract video information: $reason');
}

class QualityNotAvailableFailure extends DownloadFailure {
  const QualityNotAvailableFailure({required String quality})
      : super(message: 'Quality "$quality" is not available for this video');
}

/// 🎵 Audio Related Failures
class AudioFailure extends Failure {
  const AudioFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class AudioExtractionFailure extends AudioFailure {
  const AudioExtractionFailure({required String reason})
      : super(message: 'Failed to extract audio: $reason');
}

class AudioConversionFailure extends AudioFailure {
  const AudioConversionFailure({required String format})
      : super(message: 'Failed to convert audio to $format format');
}

/// 🎬 Video Related Failures
class VideoFailure extends Failure {
  const VideoFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class VideoProcessingFailure extends VideoFailure {
  const VideoProcessingFailure({required String reason})
      : super(message: 'Video processing failed: $reason');
}

class VideoConversionFailure extends VideoFailure {
  const VideoConversionFailure({required String format})
      : super(message: 'Failed to convert video to $format format');
}

class WatermarkRemovalFailure extends VideoFailure {
  const WatermarkRemovalFailure({required String reason})
      : super(message: 'Watermark removal failed: $reason');
}

/// 🗄️ Database Related Failures
class DatabaseFailure extends Failure {
  const DatabaseFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class DatabaseConnectionFailure extends DatabaseFailure {
  const DatabaseConnectionFailure()
      : super(message: 'Failed to connect to database');
}

class DatabaseOperationFailure extends DatabaseFailure {
  const DatabaseOperationFailure({required String operation})
      : super(message: 'Database $operation operation failed');
}

/// 🔧 Validation Related Failures
class ValidationFailure extends Failure {
  const ValidationFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class EmptyUrlFailure extends ValidationFailure {
  const EmptyUrlFailure() : super(message: 'URL cannot be empty');
}

class InvalidFormatFailure extends ValidationFailure {
  const InvalidFormatFailure({required String format})
      : super(message: 'Invalid format: $format');
}

class FileSizeExceededFailure extends ValidationFailure {
  const FileSizeExceededFailure({required String maxSize})
      : super(message: 'File size exceeds maximum limit of $maxSize');
}

/// 🔄 Conversion Related Failures
class ConversionFailure extends Failure {
  const ConversionFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class UnsupportedConversionFailure extends ConversionFailure {
  const UnsupportedConversionFailure({
    required String fromFormat,
    required String toFormat,
  }) : super(message: 'Conversion from $fromFormat to $toFormat is not supported');
}

class ConversionTimeoutFailure extends ConversionFailure {
  const ConversionTimeoutFailure()
      : super(message: 'Conversion process timed out');
}

/// ☁️ Cloud Related Failures
class CloudFailure extends Failure {
  const CloudFailure({
    required super.message,
    super.code,
    super.data,
  });
}

class CloudUploadFailure extends CloudFailure {
  const CloudUploadFailure({required String provider})
      : super(message: 'Failed to upload to $provider');
}

class CloudAuthenticationFailure extends CloudFailure {
  const CloudAuthenticationFailure({required String provider})
      : super(message: 'Authentication failed for $provider');
}

/// 🔧 General Application Failures
class UnknownFailure extends Failure {
  const UnknownFailure({String? message})
      : super(message: message ?? 'An unknown error occurred');
}

class CacheFailure extends Failure {
  const CacheFailure({required super.message});
}

class ConfigurationFailure extends Failure {
  const ConfigurationFailure({required super.message});
}
