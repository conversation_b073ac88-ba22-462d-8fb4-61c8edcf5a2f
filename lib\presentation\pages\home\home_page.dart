import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:lottie/lottie.dart';

import '../../../core/constants/app_constants.dart';
import '../../blocs/video_extraction/video_extraction_bloc.dart';
import '../../blocs/download/download_bloc.dart';
import '../../widgets/common/gradient_background.dart';
import '../../widgets/common/glass_card.dart';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/video/video_info_card.dart';
import '../../widgets/video/quality_selector_sheet.dart';

/// 🏠 Professional Home Page
/// Main interface for video downloading with modern Material Design 3
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _urlFocusNode = FocusNode();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupUrlListener();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  void _setupUrlListener() {
    _urlController.addListener(() {
      final text = _urlController.text;
      if (text.isNotEmpty) {
        // Auto-detect platform and show suggestions
        context.read<VideoExtractionBloc>().add(
          VideoExtractionPlatformDetected(url: text),
        );
      }
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _urlFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientBackground(
        child: SafeArea(
          child: BlocListener<VideoExtractionBloc, VideoExtractionState>(
            listener: _handleVideoExtractionState,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildAppBar(),
                SliverPadding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      _buildMainContent(),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        title: FadeTransition(
          opacity: _fadeAnimation,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.secondary,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.video_library_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                AppConstants.appName,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
        centerTitle: false,
      ),
      actions: [
        IconButton(
          onPressed: () => _showSettingsSheet(),
          icon: Icon(
            Icons.settings_rounded,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        IconButton(
          onPressed: () => _showHelpSheet(),
          icon: Icon(
            Icons.help_outline_rounded,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent() {
    return AnimationLimiter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 600),
          childAnimationBuilder: (widget) => SlideAnimation(
            verticalOffset: 50.0,
            child: FadeInAnimation(child: widget),
          ),
          children: [
            _buildUrlInputSection(),
            const SizedBox(height: 24),
            _buildPlatformSelector(),
            const SizedBox(height: 24),
            _buildQuickActions(),
            const SizedBox(height: 24),
            _buildVideoInfoSection(),
            const SizedBox(height: 24),
            _buildStatisticsSection(),
            const SizedBox(height: 24),
            _buildRecentDownloads(),
          ],
        ),
      ),
    );
  }

  Widget _buildUrlInputSection() {
    return GlassCard(
      child: UrlInputSection(
        controller: _urlController,
        focusNode: _urlFocusNode,
        onSubmitted: _handleUrlSubmitted,
        onPaste: _handlePasteFromClipboard,
        onClear: _handleClearUrl,
      ),
    );
  }

  Widget _buildPlatformSelector() {
    return BlocBuilder<VideoExtractionBloc, VideoExtractionState>(
      builder: (context, state) {
        if (state is VideoExtractionPlatformDetected) {
          return GlassCard(
            child: PlatformSelector(
              detectedPlatform: state.platform,
              supportedPlatforms: state.supportedPlatforms,
              onPlatformSelected: _handlePlatformSelected,
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildQuickActions() {
    return GlassCard(
      child: QuickActions(
        onBatchDownload: _handleBatchDownload,
        onPlaylistDownload: _handlePlaylistDownload,
        onAudioExtraction: _handleAudioExtraction,
        onScheduleDownload: _handleScheduleDownload,
      ),
    );
  }

  Widget _buildVideoInfoSection() {
    return BlocBuilder<VideoExtractionBloc, VideoExtractionState>(
      builder: (context, state) {
        if (state is VideoExtractionLoading) {
          return GlassCard(
            child: Container(
              height: 200,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset(
                      'assets/animations/loading.json',
                      width: 80,
                      height: 80,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Extracting video information...',
                      style: Theme.of(context).textTheme.bodyLarge,
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        if (state is VideoExtractionSuccess) {
          return GlassCard(
            child: VideoInfoCard(
              videoInfo: state.videoInfo,
              onDownload: _handleDownloadVideo,
              onAudioExtract: _handleExtractAudio,
              onShare: _handleShareVideo,
            ),
          );
        }

        if (state is VideoExtractionFailure) {
          return GlassCard(
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline_rounded,
                    size: 48,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to extract video',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  FilledButton.icon(
                    onPressed: _handleRetryExtraction,
                    icon: const Icon(Icons.refresh_rounded),
                    label: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildStatisticsSection() {
    return BlocBuilder<DownloadBloc, DownloadState>(
      builder: (context, state) {
        return GlassCard(
          child: StatisticsCard(
            totalDownloads: state.totalDownloads,
            activeDownloads: state.activeDownloads.length,
            completedToday: state.completedToday,
            totalDataSaved: state.totalDataSaved,
          ),
        );
      },
    );
  }

  Widget _buildRecentDownloads() {
    return BlocBuilder<DownloadBloc, DownloadState>(
      builder: (context, state) {
        if (state.recentDownloads.isEmpty) {
          return const SizedBox.shrink();
        }

        return GlassCard(
          child: RecentDownloads(
            downloads: state.recentDownloads.take(5).toList(),
            onViewAll: _handleViewAllDownloads,
            onItemTap: _handleDownloadItemTap,
          ),
        );
      },
    );
  }

  // Event Handlers
  void _handleVideoExtractionState(BuildContext context, VideoExtractionState state) {
    if (state is VideoExtractionSuccess) {
      HapticFeedback.lightImpact();
      _showSuccessSnackBar('Video information extracted successfully!');
    } else if (state is VideoExtractionFailure) {
      HapticFeedback.heavyImpact();
      _showErrorSnackBar(state.message);
    }
  }

  void _handleUrlSubmitted(String url) {
    if (url.trim().isEmpty) {
      _showErrorSnackBar('Please enter a valid URL');
      return;
    }

    context.read<VideoExtractionBloc>().add(
      VideoExtractionRequested(url: url.trim()),
    );
  }

  Future<void> _handlePasteFromClipboard() async {
    final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
    if (clipboardData?.text != null) {
      _urlController.text = clipboardData!.text!;
      _handleUrlSubmitted(clipboardData.text!);
    }
  }

  void _handleClearUrl() {
    _urlController.clear();
    context.read<VideoExtractionBloc>().add(const VideoExtractionCleared());
  }

  void _handlePlatformSelected(String platformId) {
    // Handle platform-specific settings
  }

  void _handleDownloadVideo(VideoInfo videoInfo, VideoQuality quality) {
    context.read<DownloadBloc>().add(
      DownloadVideoRequested(
        videoInfo: videoInfo,
        quality: quality,
      ),
    );
    _showSuccessSnackBar('Download started!');
  }

  void _handleExtractAudio(VideoInfo videoInfo, AudioQuality quality) {
    context.read<DownloadBloc>().add(
      DownloadAudioRequested(
        videoInfo: videoInfo,
        quality: quality,
      ),
    );
    _showSuccessSnackBar('Audio extraction started!');
  }

  void _handleShareVideo(VideoInfo videoInfo) {
    // Implement sharing functionality
  }

  void _handleRetryExtraction() {
    if (_urlController.text.trim().isNotEmpty) {
      _handleUrlSubmitted(_urlController.text.trim());
    }
  }

  void _handleBatchDownload() {
    // Navigate to batch download page
  }

  void _handlePlaylistDownload() {
    // Navigate to playlist download page
  }

  void _handleAudioExtraction() {
    // Navigate to audio extraction page
  }

  void _handleScheduleDownload() {
    // Navigate to schedule download page
  }

  void _handleViewAllDownloads() {
    // Navigate to downloads page
  }

  void _handleDownloadItemTap(DownloadItem item) {
    // Show download details
  }

  void _showSettingsSheet() {
    // Show settings bottom sheet
  }

  void _showHelpSheet() {
    // Show help bottom sheet
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle_rounded,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.error_rounded,
              color: Theme.of(context).colorScheme.onError,
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
