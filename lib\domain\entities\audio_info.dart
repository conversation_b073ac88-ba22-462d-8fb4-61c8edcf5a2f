import 'package:equatable/equatable.dart';

/// 🎵 Audio Information Entity
/// Represents audio metadata and extraction options
class AudioInfo extends Equatable {
  final String id;
  final String title;
  final String? artist;
  final String? album;
  final String? genre;
  final Duration? duration;
  final String? thumbnailUrl;
  final String sourceUrl;
  final String platform;
  final List<AudioFormat> availableFormats;
  final AudioMetadata? metadata;
  final DateTime? extractedAt;

  const AudioInfo({
    required this.id,
    required this.title,
    this.artist,
    this.album,
    this.genre,
    this.duration,
    this.thumbnailUrl,
    required this.sourceUrl,
    required this.platform,
    required this.availableFormats,
    this.metadata,
    this.extractedAt,
  });

  /// Get the best available audio format
  AudioFormat? get bestFormat {
    if (availableFormats.isEmpty) return null;
    
    // Sort by bitrate (descending) and prefer lossless formats
    final sortedFormats = List<AudioFormat>.from(availableFormats)
      ..sort((a, b) {
        // Prefer lossless formats
        if (a.isLossless && !b.isLossless) return -1;
        if (!a.isLossless && b.isLossless) return 1;
        
        // Then sort by bitrate
        return b.bitrate.compareTo(a.bitrate);
      });
    
    return sortedFormats.first;
  }

  /// Get format by type
  AudioFormat? getFormatByType(String format) {
    try {
      return availableFormats.firstWhere((f) => f.format.toLowerCase() == format.toLowerCase());
    } catch (e) {
      return null;
    }
  }

  /// Get format by bitrate
  AudioFormat? getFormatByBitrate(int bitrate) {
    try {
      return availableFormats.firstWhere((f) => f.bitrate == bitrate);
    } catch (e) {
      return null;
    }
  }

  /// Check if audio has specific format
  bool hasFormat(String format) {
    return availableFormats.any((f) => f.format.toLowerCase() == format.toLowerCase());
  }

  /// Get formatted duration
  String get formattedDuration {
    if (duration == null) return 'Unknown';
    
    final minutes = duration!.inMinutes;
    final seconds = duration!.inSeconds.remainder(60);
    
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Get display title with artist
  String get displayTitle {
    if (artist != null && artist!.isNotEmpty) {
      return '$artist - $title';
    }
    return title;
  }

  /// Copy with new values
  AudioInfo copyWith({
    String? id,
    String? title,
    String? artist,
    String? album,
    String? genre,
    Duration? duration,
    String? thumbnailUrl,
    String? sourceUrl,
    String? platform,
    List<AudioFormat>? availableFormats,
    AudioMetadata? metadata,
    DateTime? extractedAt,
  }) {
    return AudioInfo(
      id: id ?? this.id,
      title: title ?? this.title,
      artist: artist ?? this.artist,
      album: album ?? this.album,
      genre: genre ?? this.genre,
      duration: duration ?? this.duration,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      sourceUrl: sourceUrl ?? this.sourceUrl,
      platform: platform ?? this.platform,
      availableFormats: availableFormats ?? this.availableFormats,
      metadata: metadata ?? this.metadata,
      extractedAt: extractedAt ?? this.extractedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        artist,
        album,
        genre,
        duration,
        thumbnailUrl,
        sourceUrl,
        platform,
        availableFormats,
        metadata,
        extractedAt,
      ];

  @override
  String toString() => 'AudioInfo(id: $id, title: $title, artist: $artist, formats: ${availableFormats.length})';
}

/// 🎵 Audio Format Information
class AudioFormat extends Equatable {
  final String format; // e.g., "mp3", "aac", "flac"
  final String downloadUrl;
  final int fileSize; // in bytes
  final int bitrate; // in kbps
  final int sampleRate; // in Hz
  final int channels; // number of audio channels
  final String? codec; // audio codec
  final bool isLossless;
  final AudioQualityLevel qualityLevel;

  const AudioFormat({
    required this.format,
    required this.downloadUrl,
    required this.fileSize,
    required this.bitrate,
    required this.sampleRate,
    required this.channels,
    this.codec,
    required this.isLossless,
    required this.qualityLevel,
  });

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize < 1024) {
      return '${fileSize}B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    } else if (fileSize < 1024 * 1024 * 1024) {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// Get formatted sample rate
  String get formattedSampleRate {
    if (sampleRate >= 1000) {
      return '${(sampleRate / 1000).toStringAsFixed(1)}kHz';
    } else {
      return '${sampleRate}Hz';
    }
  }

  /// Get channel description
  String get channelDescription {
    switch (channels) {
      case 1:
        return 'Mono';
      case 2:
        return 'Stereo';
      case 6:
        return '5.1 Surround';
      case 8:
        return '7.1 Surround';
      default:
        return '${channels}ch';
    }
  }

  /// Get quality description
  String get qualityDescription {
    final buffer = StringBuffer();
    buffer.write('${bitrate}kbps');
    buffer.write(' • $formattedSampleRate');
    buffer.write(' • $channelDescription');
    
    if (isLossless) {
      buffer.write(' • Lossless');
    }
    
    return buffer.toString();
  }

  /// Get display name
  String get displayName {
    return '${format.toUpperCase()} - $qualityDescription';
  }

  @override
  List<Object?> get props => [
        format,
        downloadUrl,
        fileSize,
        bitrate,
        sampleRate,
        channels,
        codec,
        isLossless,
        qualityLevel,
      ];

  @override
  String toString() => 'AudioFormat(format: $format, bitrate: ${bitrate}kbps, size: $formattedFileSize)';
}

/// 🎵 Audio Quality Levels
enum AudioQualityLevel {
  low,      // < 128kbps
  medium,   // 128-192kbps
  high,     // 192-320kbps
  lossless, // FLAC, WAV, etc.
}

extension AudioQualityLevelX on AudioQualityLevel {
  String get displayName {
    switch (this) {
      case AudioQualityLevel.low:
        return 'Low Quality';
      case AudioQualityLevel.medium:
        return 'Medium Quality';
      case AudioQualityLevel.high:
        return 'High Quality';
      case AudioQualityLevel.lossless:
        return 'Lossless';
    }
  }

  String get description {
    switch (this) {
      case AudioQualityLevel.low:
        return 'Good for voice content';
      case AudioQualityLevel.medium:
        return 'Good for most music';
      case AudioQualityLevel.high:
        return 'Excellent for music';
      case AudioQualityLevel.lossless:
        return 'Perfect audio quality';
    }
  }
}

/// 🎵 Audio Metadata
class AudioMetadata extends Equatable {
  final String? albumArtist;
  final String? composer;
  final String? year;
  final int? trackNumber;
  final int? totalTracks;
  final int? discNumber;
  final int? totalDiscs;
  final String? comment;
  final String? lyrics;
  final List<String>? tags;
  final Map<String, dynamic>? customMetadata;

  const AudioMetadata({
    this.albumArtist,
    this.composer,
    this.year,
    this.trackNumber,
    this.totalTracks,
    this.discNumber,
    this.totalDiscs,
    this.comment,
    this.lyrics,
    this.tags,
    this.customMetadata,
  });

  /// Get formatted track number
  String? get formattedTrackNumber {
    if (trackNumber == null) return null;
    
    if (totalTracks != null) {
      return '${trackNumber.toString().padLeft(2, '0')}/${totalTracks.toString().padLeft(2, '0')}';
    } else {
      return trackNumber.toString().padLeft(2, '0');
    }
  }

  /// Get formatted disc number
  String? get formattedDiscNumber {
    if (discNumber == null) return null;
    
    if (totalDiscs != null) {
      return '$discNumber/$totalDiscs';
    } else {
      return discNumber.toString();
    }
  }

  @override
  List<Object?> get props => [
        albumArtist,
        composer,
        year,
        trackNumber,
        totalTracks,
        discNumber,
        totalDiscs,
        comment,
        lyrics,
        tags,
        customMetadata,
      ];

  @override
  String toString() => 'AudioMetadata(artist: $albumArtist, year: $year, track: $formattedTrackNumber)';
}

/// 🎵 Audio Extraction Options
class AudioExtractionOptions extends Equatable {
  final String format;
  final int bitrate;
  final int sampleRate;
  final int channels;
  final bool normalizeAudio;
  final bool removeNoise;
  final double? startTime; // in seconds
  final double? endTime; // in seconds
  final Map<String, dynamic>? customOptions;

  const AudioExtractionOptions({
    required this.format,
    required this.bitrate,
    required this.sampleRate,
    required this.channels,
    this.normalizeAudio = false,
    this.removeNoise = false,
    this.startTime,
    this.endTime,
    this.customOptions,
  });

  /// Get FFmpeg arguments for extraction
  List<String> get ffmpegArgs {
    final args = <String>[];
    
    // Audio codec
    switch (format.toLowerCase()) {
      case 'mp3':
        args.addAll(['-acodec', 'libmp3lame']);
        break;
      case 'aac':
        args.addAll(['-acodec', 'aac']);
        break;
      case 'flac':
        args.addAll(['-acodec', 'flac']);
        break;
      case 'ogg':
        args.addAll(['-acodec', 'libvorbis']);
        break;
    }
    
    // Bitrate
    args.addAll(['-ab', '${bitrate}k']);
    
    // Sample rate
    args.addAll(['-ar', sampleRate.toString()]);
    
    // Channels
    args.addAll(['-ac', channels.toString()]);
    
    // Time range
    if (startTime != null) {
      args.addAll(['-ss', startTime.toString()]);
    }
    
    if (endTime != null) {
      args.addAll(['-to', endTime.toString()]);
    }
    
    // Audio filters
    final filters = <String>[];
    
    if (normalizeAudio) {
      filters.add('loudnorm');
    }
    
    if (removeNoise) {
      filters.add('afftdn');
    }
    
    if (filters.isNotEmpty) {
      args.addAll(['-af', filters.join(',')]);
    }
    
    return args;
  }

  @override
  List<Object?> get props => [
        format,
        bitrate,
        sampleRate,
        channels,
        normalizeAudio,
        removeNoise,
        startTime,
        endTime,
        customOptions,
      ];

  @override
  String toString() => 'AudioExtractionOptions(format: $format, bitrate: ${bitrate}kbps)';
}
