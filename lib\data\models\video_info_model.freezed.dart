// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'video_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VideoInfoModel _$VideoInfoModelFromJson(Map<String, dynamic> json) {
  return _VideoInfoModel.fromJson(json);
}

/// @nodoc
mixin _$VideoInfoModel {
  @HiveField(0)
  String get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String get title => throw _privateConstructorUsedError;
  @HiveField(2)
  String get url => throw _privateConstructorUsedError;
  @HiveField(3)
  String get thumbnailUrl => throw _privateConstructorUsedError;
  @HiveField(4)
  String get platform => throw _privateConstructorUsedError;
  @HiveField(5)
  int get durationInSeconds => throw _privateConstructorUsedError;
  @HiveField(6)
  List<VideoQualityModel> get availableQualities =>
      throw _privateConstructorUsedError;
  @HiveField(7)
  String? get description => throw _privateConstructorUsedError;
  @HiveField(8)
  String? get uploader => throw _privateConstructorUsedError;
  @HiveField(9)
  DateTime? get uploadDate => throw _privateConstructorUsedError;
  @HiveField(10)
  int? get viewCount => throw _privateConstructorUsedError;
  @HiveField(11)
  int? get likeCount => throw _privateConstructorUsedError;
  @HiveField(12)
  List<String>? get tags => throw _privateConstructorUsedError;
  @HiveField(13)
  bool? get hasWatermark => throw _privateConstructorUsedError;
  @HiveField(14)
  DateTime? get extractedAt => throw _privateConstructorUsedError;

  /// Serializes this VideoInfoModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VideoInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VideoInfoModelCopyWith<VideoInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VideoInfoModelCopyWith<$Res> {
  factory $VideoInfoModelCopyWith(
          VideoInfoModel value, $Res Function(VideoInfoModel) then) =
      _$VideoInfoModelCopyWithImpl<$Res, VideoInfoModel>;
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String title,
      @HiveField(2) String url,
      @HiveField(3) String thumbnailUrl,
      @HiveField(4) String platform,
      @HiveField(5) int durationInSeconds,
      @HiveField(6) List<VideoQualityModel> availableQualities,
      @HiveField(7) String? description,
      @HiveField(8) String? uploader,
      @HiveField(9) DateTime? uploadDate,
      @HiveField(10) int? viewCount,
      @HiveField(11) int? likeCount,
      @HiveField(12) List<String>? tags,
      @HiveField(13) bool? hasWatermark,
      @HiveField(14) DateTime? extractedAt});
}

/// @nodoc
class _$VideoInfoModelCopyWithImpl<$Res, $Val extends VideoInfoModel>
    implements $VideoInfoModelCopyWith<$Res> {
  _$VideoInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VideoInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? url = null,
    Object? thumbnailUrl = null,
    Object? platform = null,
    Object? durationInSeconds = null,
    Object? availableQualities = null,
    Object? description = freezed,
    Object? uploader = freezed,
    Object? uploadDate = freezed,
    Object? viewCount = freezed,
    Object? likeCount = freezed,
    Object? tags = freezed,
    Object? hasWatermark = freezed,
    Object? extractedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      durationInSeconds: null == durationInSeconds
          ? _value.durationInSeconds
          : durationInSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      availableQualities: null == availableQualities
          ? _value.availableQualities
          : availableQualities // ignore: cast_nullable_to_non_nullable
              as List<VideoQualityModel>,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      uploader: freezed == uploader
          ? _value.uploader
          : uploader // ignore: cast_nullable_to_non_nullable
              as String?,
      uploadDate: freezed == uploadDate
          ? _value.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      viewCount: freezed == viewCount
          ? _value.viewCount
          : viewCount // ignore: cast_nullable_to_non_nullable
              as int?,
      likeCount: freezed == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      hasWatermark: freezed == hasWatermark
          ? _value.hasWatermark
          : hasWatermark // ignore: cast_nullable_to_non_nullable
              as bool?,
      extractedAt: freezed == extractedAt
          ? _value.extractedAt
          : extractedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VideoInfoModelImplCopyWith<$Res>
    implements $VideoInfoModelCopyWith<$Res> {
  factory _$$VideoInfoModelImplCopyWith(_$VideoInfoModelImpl value,
          $Res Function(_$VideoInfoModelImpl) then) =
      __$$VideoInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String title,
      @HiveField(2) String url,
      @HiveField(3) String thumbnailUrl,
      @HiveField(4) String platform,
      @HiveField(5) int durationInSeconds,
      @HiveField(6) List<VideoQualityModel> availableQualities,
      @HiveField(7) String? description,
      @HiveField(8) String? uploader,
      @HiveField(9) DateTime? uploadDate,
      @HiveField(10) int? viewCount,
      @HiveField(11) int? likeCount,
      @HiveField(12) List<String>? tags,
      @HiveField(13) bool? hasWatermark,
      @HiveField(14) DateTime? extractedAt});
}

/// @nodoc
class __$$VideoInfoModelImplCopyWithImpl<$Res>
    extends _$VideoInfoModelCopyWithImpl<$Res, _$VideoInfoModelImpl>
    implements _$$VideoInfoModelImplCopyWith<$Res> {
  __$$VideoInfoModelImplCopyWithImpl(
      _$VideoInfoModelImpl _value, $Res Function(_$VideoInfoModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of VideoInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? url = null,
    Object? thumbnailUrl = null,
    Object? platform = null,
    Object? durationInSeconds = null,
    Object? availableQualities = null,
    Object? description = freezed,
    Object? uploader = freezed,
    Object? uploadDate = freezed,
    Object? viewCount = freezed,
    Object? likeCount = freezed,
    Object? tags = freezed,
    Object? hasWatermark = freezed,
    Object? extractedAt = freezed,
  }) {
    return _then(_$VideoInfoModelImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      url: null == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: null == thumbnailUrl
          ? _value.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String,
      platform: null == platform
          ? _value.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
      durationInSeconds: null == durationInSeconds
          ? _value.durationInSeconds
          : durationInSeconds // ignore: cast_nullable_to_non_nullable
              as int,
      availableQualities: null == availableQualities
          ? _value._availableQualities
          : availableQualities // ignore: cast_nullable_to_non_nullable
              as List<VideoQualityModel>,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      uploader: freezed == uploader
          ? _value.uploader
          : uploader // ignore: cast_nullable_to_non_nullable
              as String?,
      uploadDate: freezed == uploadDate
          ? _value.uploadDate
          : uploadDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      viewCount: freezed == viewCount
          ? _value.viewCount
          : viewCount // ignore: cast_nullable_to_non_nullable
              as int?,
      likeCount: freezed == likeCount
          ? _value.likeCount
          : likeCount // ignore: cast_nullable_to_non_nullable
              as int?,
      tags: freezed == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      hasWatermark: freezed == hasWatermark
          ? _value.hasWatermark
          : hasWatermark // ignore: cast_nullable_to_non_nullable
              as bool?,
      extractedAt: freezed == extractedAt
          ? _value.extractedAt
          : extractedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VideoInfoModelImpl implements _VideoInfoModel {
  const _$VideoInfoModelImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) required this.title,
      @HiveField(2) required this.url,
      @HiveField(3) required this.thumbnailUrl,
      @HiveField(4) required this.platform,
      @HiveField(5) required this.durationInSeconds,
      @HiveField(6) required final List<VideoQualityModel> availableQualities,
      @HiveField(7) this.description,
      @HiveField(8) this.uploader,
      @HiveField(9) this.uploadDate,
      @HiveField(10) this.viewCount,
      @HiveField(11) this.likeCount,
      @HiveField(12) final List<String>? tags,
      @HiveField(13) this.hasWatermark,
      @HiveField(14) this.extractedAt})
      : _availableQualities = availableQualities,
        _tags = tags;

  factory _$VideoInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$VideoInfoModelImplFromJson(json);

  @override
  @HiveField(0)
  final String id;
  @override
  @HiveField(1)
  final String title;
  @override
  @HiveField(2)
  final String url;
  @override
  @HiveField(3)
  final String thumbnailUrl;
  @override
  @HiveField(4)
  final String platform;
  @override
  @HiveField(5)
  final int durationInSeconds;
  final List<VideoQualityModel> _availableQualities;
  @override
  @HiveField(6)
  List<VideoQualityModel> get availableQualities {
    if (_availableQualities is EqualUnmodifiableListView)
      return _availableQualities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableQualities);
  }

  @override
  @HiveField(7)
  final String? description;
  @override
  @HiveField(8)
  final String? uploader;
  @override
  @HiveField(9)
  final DateTime? uploadDate;
  @override
  @HiveField(10)
  final int? viewCount;
  @override
  @HiveField(11)
  final int? likeCount;
  final List<String>? _tags;
  @override
  @HiveField(12)
  List<String>? get tags {
    final value = _tags;
    if (value == null) return null;
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @HiveField(13)
  final bool? hasWatermark;
  @override
  @HiveField(14)
  final DateTime? extractedAt;

  @override
  String toString() {
    return 'VideoInfoModel(id: $id, title: $title, url: $url, thumbnailUrl: $thumbnailUrl, platform: $platform, durationInSeconds: $durationInSeconds, availableQualities: $availableQualities, description: $description, uploader: $uploader, uploadDate: $uploadDate, viewCount: $viewCount, likeCount: $likeCount, tags: $tags, hasWatermark: $hasWatermark, extractedAt: $extractedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VideoInfoModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.durationInSeconds, durationInSeconds) ||
                other.durationInSeconds == durationInSeconds) &&
            const DeepCollectionEquality()
                .equals(other._availableQualities, _availableQualities) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.uploader, uploader) ||
                other.uploader == uploader) &&
            (identical(other.uploadDate, uploadDate) ||
                other.uploadDate == uploadDate) &&
            (identical(other.viewCount, viewCount) ||
                other.viewCount == viewCount) &&
            (identical(other.likeCount, likeCount) ||
                other.likeCount == likeCount) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.hasWatermark, hasWatermark) ||
                other.hasWatermark == hasWatermark) &&
            (identical(other.extractedAt, extractedAt) ||
                other.extractedAt == extractedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      url,
      thumbnailUrl,
      platform,
      durationInSeconds,
      const DeepCollectionEquality().hash(_availableQualities),
      description,
      uploader,
      uploadDate,
      viewCount,
      likeCount,
      const DeepCollectionEquality().hash(_tags),
      hasWatermark,
      extractedAt);

  /// Create a copy of VideoInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VideoInfoModelImplCopyWith<_$VideoInfoModelImpl> get copyWith =>
      __$$VideoInfoModelImplCopyWithImpl<_$VideoInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VideoInfoModelImplToJson(
      this,
    );
  }
}

abstract class _VideoInfoModel implements VideoInfoModel {
  const factory _VideoInfoModel(
      {@HiveField(0) required final String id,
      @HiveField(1) required final String title,
      @HiveField(2) required final String url,
      @HiveField(3) required final String thumbnailUrl,
      @HiveField(4) required final String platform,
      @HiveField(5) required final int durationInSeconds,
      @HiveField(6) required final List<VideoQualityModel> availableQualities,
      @HiveField(7) final String? description,
      @HiveField(8) final String? uploader,
      @HiveField(9) final DateTime? uploadDate,
      @HiveField(10) final int? viewCount,
      @HiveField(11) final int? likeCount,
      @HiveField(12) final List<String>? tags,
      @HiveField(13) final bool? hasWatermark,
      @HiveField(14) final DateTime? extractedAt}) = _$VideoInfoModelImpl;

  factory _VideoInfoModel.fromJson(Map<String, dynamic> json) =
      _$VideoInfoModelImpl.fromJson;

  @override
  @HiveField(0)
  String get id;
  @override
  @HiveField(1)
  String get title;
  @override
  @HiveField(2)
  String get url;
  @override
  @HiveField(3)
  String get thumbnailUrl;
  @override
  @HiveField(4)
  String get platform;
  @override
  @HiveField(5)
  int get durationInSeconds;
  @override
  @HiveField(6)
  List<VideoQualityModel> get availableQualities;
  @override
  @HiveField(7)
  String? get description;
  @override
  @HiveField(8)
  String? get uploader;
  @override
  @HiveField(9)
  DateTime? get uploadDate;
  @override
  @HiveField(10)
  int? get viewCount;
  @override
  @HiveField(11)
  int? get likeCount;
  @override
  @HiveField(12)
  List<String>? get tags;
  @override
  @HiveField(13)
  bool? get hasWatermark;
  @override
  @HiveField(14)
  DateTime? get extractedAt;

  /// Create a copy of VideoInfoModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VideoInfoModelImplCopyWith<_$VideoInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
