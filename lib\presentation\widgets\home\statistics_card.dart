import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

/// 📊 Statistics Card Widget
/// Displays download statistics with beautiful animations
class StatisticsCard extends StatefulWidget {
  final int totalDownloads;
  final int activeDownloads;
  final int completedToday;
  final String totalDataSaved;

  const StatisticsCard({
    super.key,
    required this.totalDownloads,
    required this.activeDownloads,
    required this.completedToday,
    required this.totalDataSaved,
  });

  @override
  State<StatisticsCard> createState() => _StatisticsCardState();
}

class _StatisticsCardState extends State<StatisticsCard>
    with TickerProviderStateMixin {
  late AnimationController _countController;
  late List<Animation<int>> _countAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _countController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _countAnimations = [
      IntTween(begin: 0, end: widget.totalDownloads).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: widget.activeDownloads).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
      IntTween(begin: 0, end: widget.completedToday).animate(
        CurvedAnimation(parent: _countController, curve: Curves.easeOut),
      ),
    ];

    _countController.forward();
  }

  @override
  void didUpdateWidget(StatisticsCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.totalDownloads != widget.totalDownloads ||
        oldWidget.activeDownloads != widget.activeDownloads ||
        oldWidget.completedToday != widget.completedToday) {
      _countController.reset();
      _initializeAnimations();
    }
  }

  @override
  void dispose() {
    _countController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(context),
        const SizedBox(height: 16),
        _buildStatisticsGrid(context),
      ],
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.analytics_rounded,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          'Statistics',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.tertiaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.trending_up_rounded,
                size: 12,
                color: Theme.of(context).colorScheme.onTertiaryContainer,
              ),
              const SizedBox(width: 4),
              Text(
                'Live',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onTertiaryContainer,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsGrid(BuildContext context) {
    final statistics = [
      StatisticItem(
        icon: Icons.download_rounded,
        title: 'Total Downloads',
        value: widget.totalDownloads,
        color: Colors.blue,
        animationIndex: 0,
      ),
      StatisticItem(
        icon: Icons.downloading_rounded,
        title: 'Active Downloads',
        value: widget.activeDownloads,
        color: Colors.orange,
        animationIndex: 1,
      ),
      StatisticItem(
        icon: Icons.today_rounded,
        title: 'Completed Today',
        value: widget.completedToday,
        color: Colors.green,
        animationIndex: 2,
      ),
      StatisticItem(
        icon: Icons.storage_rounded,
        title: 'Data Saved',
        stringValue: widget.totalDataSaved,
        color: Colors.purple,
      ),
    ];

    return AnimationLimiter(
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: statistics.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 600),
            columnCount: 2,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildStatisticCard(context, statistics[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStatisticCard(BuildContext context, StatisticItem item) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            item.color.withOpacity(0.1),
            item.color.withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: item.color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: item.color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    item.icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const Spacer(),
                if (item.animationIndex != null)
                  Icon(
                    Icons.trending_up_rounded,
                    color: item.color,
                    size: 16,
                  ),
              ],
            ),
            const Spacer(),
            if (item.stringValue != null)
              Text(
                item.stringValue!,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: item.color,
                ),
              )
            else if (item.animationIndex != null)
              AnimatedBuilder(
                animation: _countAnimations[item.animationIndex!],
                builder: (context, child) {
                  return Text(
                    _formatNumber(_countAnimations[item.animationIndex!].value),
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: item.color,
                    ),
                  );
                },
              )
            else
              Text(
                _formatNumber(item.value ?? 0),
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: item.color,
                ),
              ),
            const SizedBox(height: 4),
            Text(
              item.title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toString();
    }
  }
}

/// 📈 Statistic Item Model
class StatisticItem {
  final IconData icon;
  final String title;
  final int? value;
  final String? stringValue;
  final Color color;
  final int? animationIndex;

  const StatisticItem({
    required this.icon,
    required this.title,
    this.value,
    this.stringValue,
    required this.color,
    this.animationIndex,
  });
}

/// 📊 Advanced Statistics Widget
class AdvancedStatistics extends StatelessWidget {
  final Map<String, dynamic> statistics;

  const AdvancedStatistics({
    super.key,
    required this.statistics,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Detailed Statistics',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildProgressIndicators(context),
        const SizedBox(height: 16),
        _buildPlatformBreakdown(context),
      ],
    );
  }

  Widget _buildProgressIndicators(BuildContext context) {
    return Column(
      children: [
        _buildProgressItem(
          context,
          'Success Rate',
          statistics['successRate'] ?? 0.0,
          Colors.green,
        ),
        const SizedBox(height: 12),
        _buildProgressItem(
          context,
          'Average Speed',
          statistics['averageSpeed'] ?? 0.0,
          Colors.blue,
        ),
        const SizedBox(height: 12),
        _buildProgressItem(
          context,
          'Storage Usage',
          statistics['storageUsage'] ?? 0.0,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildProgressItem(
    BuildContext context,
    String label,
    double value,
    Color color,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(value * 100).toStringAsFixed(1)}%',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: value,
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  Widget _buildPlatformBreakdown(BuildContext context) {
    final platforms = statistics['platformBreakdown'] as Map<String, int>? ?? {};
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Platform Breakdown',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...platforms.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: _getPlatformColor(entry.key),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      entry.key,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                  Text(
                    entry.value.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            )),
      ],
    );
  }

  Color _getPlatformColor(String platform) {
    switch (platform.toLowerCase()) {
      case 'youtube':
        return Colors.red;
      case 'tiktok':
        return Colors.black;
      case 'instagram':
        return Colors.purple;
      case 'facebook':
        return Colors.blue;
      case 'twitter':
        return Colors.lightBlue;
      default:
        return Colors.grey;
    }
  }
}
