// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_quality_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VideoQualityModelAdapter extends TypeAdapter<VideoQualityModel> {
  @override
  final int typeId = 1;

  @override
  VideoQualityModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VideoQualityModel(
      quality: fields[0] as String,
      format: fields[1] as String,
      downloadUrl: fields[2] as String,
      fileSize: fields[3] as int?,
      hasAudio: fields[4] as bool,
      hasVideo: fields[5] as bool,
      codec: fields[6] as String?,
      bitrate: fields[7] as int?,
      resolution: fields[8] as String?,
      fps: fields[9] as double?,
      isHdr: fields[10] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, VideoQualityModel obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.quality)
      ..writeByte(1)
      ..write(obj.format)
      ..writeByte(2)
      ..write(obj.downloadUrl)
      ..writeByte(3)
      ..write(obj.fileSize)
      ..writeByte(4)
      ..write(obj.hasAudio)
      ..writeByte(5)
      ..write(obj.hasVideo)
      ..writeByte(6)
      ..write(obj.codec)
      ..writeByte(7)
      ..write(obj.bitrate)
      ..writeByte(8)
      ..write(obj.resolution)
      ..writeByte(9)
      ..write(obj.fps)
      ..writeByte(10)
      ..write(obj.isHdr);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VideoQualityModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VideoQualityModelImpl _$$VideoQualityModelImplFromJson(
        Map<String, dynamic> json) =>
    _$VideoQualityModelImpl(
      quality: json['quality'] as String,
      format: json['format'] as String,
      downloadUrl: json['downloadUrl'] as String,
      fileSize: (json['fileSize'] as num?)?.toInt(),
      hasAudio: json['hasAudio'] as bool,
      hasVideo: json['hasVideo'] as bool,
      codec: json['codec'] as String?,
      bitrate: (json['bitrate'] as num?)?.toInt(),
      resolution: json['resolution'] as String?,
      fps: (json['fps'] as num?)?.toDouble(),
      isHdr: json['isHdr'] as bool?,
    );

Map<String, dynamic> _$$VideoQualityModelImplToJson(
        _$VideoQualityModelImpl instance) =>
    <String, dynamic>{
      'quality': instance.quality,
      'format': instance.format,
      'downloadUrl': instance.downloadUrl,
      'fileSize': instance.fileSize,
      'hasAudio': instance.hasAudio,
      'hasVideo': instance.hasVideo,
      'codec': instance.codec,
      'bitrate': instance.bitrate,
      'resolution': instance.resolution,
      'fps': instance.fps,
      'isHdr': instance.isHdr,
    };
