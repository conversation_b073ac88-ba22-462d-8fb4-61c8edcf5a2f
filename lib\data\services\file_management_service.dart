import 'dart:io';
import 'dart:typed_data';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../core/errors/exceptions.dart';
import '../../domain/entities/download_item.dart';

/// 📁 Advanced File Management Service
/// Handles file operations, organization, and sharing
@LazySingleton()
class FileManagementService {
  static const String _appFolderName = 'VideoDownloader';
  static const String _videosFolderName = 'Videos';
  static const String _audioFolderName = 'Audio';
  static const String _thumbnailsFolderName = 'Thumbnails';
  static const String _tempFolderName = 'Temp';

  /// Get app's main directory
  Future<Directory> getAppDirectory() async {
    final externalDir = await getExternalStorageDirectory();
    if (externalDir == null) {
      throw StorageException(message: 'External storage not available');
    }
    
    final appDir = Directory(path.join(externalDir.path, _appFolderName));
    if (!await appDir.exists()) {
      await appDir.create(recursive: true);
    }
    
    return appDir;
  }

  /// Get videos directory
  Future<Directory> getVideosDirectory() async {
    final appDir = await getAppDirectory();
    final videosDir = Directory(path.join(appDir.path, _videosFolderName));
    
    if (!await videosDir.exists()) {
      await videosDir.create(recursive: true);
    }
    
    return videosDir;
  }

  /// Get audio directory
  Future<Directory> getAudioDirectory() async {
    final appDir = await getAppDirectory();
    final audioDir = Directory(path.join(appDir.path, _audioFolderName));
    
    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }
    
    return audioDir;
  }

  /// Get thumbnails directory
  Future<Directory> getThumbnailsDirectory() async {
    final appDir = await getAppDirectory();
    final thumbnailsDir = Directory(path.join(appDir.path, _thumbnailsFolderName));
    
    if (!await thumbnailsDir.exists()) {
      await thumbnailsDir.create(recursive: true);
    }
    
    return thumbnailsDir;
  }

  /// Get temporary directory
  Future<Directory> getTempDirectory() async {
    final appDir = await getAppDirectory();
    final tempDir = Directory(path.join(appDir.path, _tempFolderName));
    
    if (!await tempDir.exists()) {
      await tempDir.create(recursive: true);
    }
    
    return tempDir;
  }

  /// Check if file exists
  Future<bool> fileExists(String filePath) async {
    final file = File(filePath);
    return await file.exists();
  }

  /// Get file size
  Future<int> getFileSize(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw FileNotFoundException(filePath: filePath);
    }
    
    return await file.length();
  }

  /// Get formatted file size
  Future<String> getFormattedFileSize(String filePath) async {
    final size = await getFileSize(filePath);
    return _formatFileSize(size);
  }

  /// Delete file
  Future<void> deleteFile(String filePath) async {
    final file = File(filePath);
    if (await file.exists()) {
      await file.delete();
    }
  }

  /// Move file
  Future<String> moveFile(String sourcePath, String destinationPath) async {
    final sourceFile = File(sourcePath);
    if (!await sourceFile.exists()) {
      throw FileNotFoundException(filePath: sourcePath);
    }

    final destinationFile = File(destinationPath);
    await destinationFile.parent.create(recursive: true);
    
    await sourceFile.copy(destinationPath);
    await sourceFile.delete();
    
    return destinationPath;
  }

  /// Copy file
  Future<String> copyFile(String sourcePath, String destinationPath) async {
    final sourceFile = File(sourcePath);
    if (!await sourceFile.exists()) {
      throw FileNotFoundException(filePath: sourcePath);
    }

    final destinationFile = File(destinationPath);
    await destinationFile.parent.create(recursive: true);
    
    await sourceFile.copy(destinationPath);
    
    return destinationPath;
  }

  /// Rename file
  Future<String> renameFile(String filePath, String newName) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw FileNotFoundException(filePath: filePath);
    }

    final directory = file.parent;
    final extension = path.extension(filePath);
    final newPath = path.join(directory.path, '$newName$extension');
    
    await file.rename(newPath);
    
    return newPath;
  }

  /// Get all downloaded files
  Future<List<FileInfo>> getAllDownloadedFiles() async {
    final files = <FileInfo>[];
    
    // Get video files
    final videosDir = await getVideosDirectory();
    final videoFiles = await _getFilesInDirectory(videosDir, FileType.video);
    files.addAll(videoFiles);
    
    // Get audio files
    final audioDir = await getAudioDirectory();
    final audioFiles = await _getFilesInDirectory(audioDir, FileType.audio);
    files.addAll(audioFiles);
    
    // Sort by modification date (newest first)
    files.sort((a, b) => b.modifiedAt.compareTo(a.modifiedAt));
    
    return files;
  }

  /// Get files by type
  Future<List<FileInfo>> getFilesByType(FileType type) async {
    switch (type) {
      case FileType.video:
        final videosDir = await getVideosDirectory();
        return await _getFilesInDirectory(videosDir, type);
      case FileType.audio:
        final audioDir = await getAudioDirectory();
        return await _getFilesInDirectory(audioDir, type);
      case FileType.thumbnail:
        final thumbnailsDir = await getThumbnailsDirectory();
        return await _getFilesInDirectory(thumbnailsDir, type);
    }
  }

  /// Get files in directory
  Future<List<FileInfo>> _getFilesInDirectory(Directory directory, FileType type) async {
    final files = <FileInfo>[];
    
    if (!await directory.exists()) {
      return files;
    }
    
    await for (final entity in directory.list()) {
      if (entity is File) {
        final fileInfo = await _createFileInfo(entity, type);
        if (fileInfo != null) {
          files.add(fileInfo);
        }
      }
    }
    
    return files;
  }

  /// Create file info from file
  Future<FileInfo?> _createFileInfo(File file, FileType type) async {
    try {
      final stat = await file.stat();
      final extension = path.extension(file.path).toLowerCase();
      
      // Check if file extension matches type
      if (!_isValidFileType(extension, type)) {
        return null;
      }
      
      return FileInfo(
        path: file.path,
        name: path.basenameWithoutExtension(file.path),
        extension: extension,
        size: stat.size,
        type: type,
        createdAt: stat.changed,
        modifiedAt: stat.modified,
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if file extension is valid for type
  bool _isValidFileType(String extension, FileType type) {
    switch (type) {
      case FileType.video:
        return ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'].contains(extension);
      case FileType.audio:
        return ['.mp3', '.aac', '.flac', '.wav', '.ogg', '.m4a'].contains(extension);
      case FileType.thumbnail:
        return ['.jpg', '.jpeg', '.png', '.webp'].contains(extension);
    }
  }

  /// Share file
  Future<void> shareFile(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw FileNotFoundException(filePath: filePath);
    }

    await Share.shareXFiles([XFile(filePath)]);
  }

  /// Open file with default app
  Future<void> openFile(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw FileNotFoundException(filePath: filePath);
    }

    final result = await OpenFile.open(filePath);
    if (result.type != ResultType.done) {
      throw FileAccessException(reason: result.message);
    }
  }

  /// Get storage usage
  Future<StorageInfo> getStorageUsage() async {
    final appDir = await getAppDirectory();
    
    int totalSize = 0;
    int videoSize = 0;
    int audioSize = 0;
    int thumbnailSize = 0;
    
    int videoCount = 0;
    int audioCount = 0;
    int thumbnailCount = 0;

    // Calculate videos
    final videosDir = await getVideosDirectory();
    if (await videosDir.exists()) {
      await for (final entity in videosDir.list(recursive: true)) {
        if (entity is File) {
          final size = await entity.length();
          videoSize += size;
          videoCount++;
        }
      }
    }

    // Calculate audio
    final audioDir = await getAudioDirectory();
    if (await audioDir.exists()) {
      await for (final entity in audioDir.list(recursive: true)) {
        if (entity is File) {
          final size = await entity.length();
          audioSize += size;
          audioCount++;
        }
      }
    }

    // Calculate thumbnails
    final thumbnailsDir = await getThumbnailsDirectory();
    if (await thumbnailsDir.exists()) {
      await for (final entity in thumbnailsDir.list(recursive: true)) {
        if (entity is File) {
          final size = await entity.length();
          thumbnailSize += size;
          thumbnailCount++;
        }
      }
    }

    totalSize = videoSize + audioSize + thumbnailSize;

    return StorageInfo(
      totalSize: totalSize,
      videoSize: videoSize,
      audioSize: audioSize,
      thumbnailSize: thumbnailSize,
      videoCount: videoCount,
      audioCount: audioCount,
      thumbnailCount: thumbnailCount,
    );
  }

  /// Clean up temporary files
  Future<void> cleanupTempFiles() async {
    final tempDir = await getTempDirectory();
    
    if (await tempDir.exists()) {
      await for (final entity in tempDir.list()) {
        if (entity is File) {
          await entity.delete();
        } else if (entity is Directory) {
          await entity.delete(recursive: true);
        }
      }
    }
  }

  /// Clean up old thumbnails
  Future<void> cleanupOldThumbnails({Duration maxAge = const Duration(days: 30)}) async {
    final thumbnailsDir = await getThumbnailsDirectory();
    
    if (!await thumbnailsDir.exists()) return;
    
    final cutoffDate = DateTime.now().subtract(maxAge);
    
    await for (final entity in thumbnailsDir.list()) {
      if (entity is File) {
        final stat = await entity.stat();
        if (stat.modified.isBefore(cutoffDate)) {
          await entity.delete();
        }
      }
    }
  }

  /// Organize files by platform
  Future<void> organizeFilesByPlatform() async {
    final files = await getAllDownloadedFiles();
    
    for (final file in files) {
      // Extract platform from filename or metadata
      final platform = _extractPlatformFromFilename(file.name);
      if (platform != null) {
        await _moveFileToPlatformFolder(file, platform);
      }
    }
  }

  /// Extract platform from filename
  String? _extractPlatformFromFilename(String filename) {
    final platforms = ['youtube', 'tiktok', 'instagram', 'facebook', 'twitter'];
    
    for (final platform in platforms) {
      if (filename.toLowerCase().contains(platform)) {
        return platform;
      }
    }
    
    return null;
  }

  /// Move file to platform folder
  Future<void> _moveFileToPlatformFolder(FileInfo fileInfo, String platform) async {
    final currentFile = File(fileInfo.path);
    
    Directory targetDir;
    if (fileInfo.type == FileType.video) {
      final videosDir = await getVideosDirectory();
      targetDir = Directory(path.join(videosDir.path, platform));
    } else {
      final audioDir = await getAudioDirectory();
      targetDir = Directory(path.join(audioDir.path, platform));
    }
    
    if (!await targetDir.exists()) {
      await targetDir.create(recursive: true);
    }
    
    final newPath = path.join(targetDir.path, path.basename(fileInfo.path));
    await currentFile.rename(newPath);
  }

  /// Check storage permissions
  Future<bool> checkStoragePermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.status;
      if (status.isDenied) {
        final result = await Permission.storage.request();
        return result.isGranted;
      }
      return status.isGranted;
    }
    
    return true; // iOS doesn't need explicit storage permission
  }

  /// Request storage permissions
  Future<bool> requestStoragePermissions() async {
    if (Platform.isAndroid) {
      final result = await Permission.storage.request();
      return result.isGranted;
    }
    
    return true;
  }

  /// Format file size
  String _formatFileSize(int bytes) {
    if (bytes >= 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    } else if (bytes >= 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else if (bytes >= 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '$bytes B';
    }
  }

  /// Generate unique filename
  String generateUniqueFilename(String baseName, String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '${baseName}_$timestamp$extension';
  }

  /// Validate filename
  bool isValidFilename(String filename) {
    // Check for invalid characters
    final invalidChars = RegExp(r'[<>:"/\\|?*]');
    return !invalidChars.hasMatch(filename) && filename.trim().isNotEmpty;
  }

  /// Sanitize filename
  String sanitizeFilename(String filename) {
    // Remove invalid characters
    String sanitized = filename.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
    
    // Remove multiple spaces and trim
    sanitized = sanitized.replaceAll(RegExp(r'\s+'), ' ').trim();
    
    // Limit length
    if (sanitized.length > 100) {
      sanitized = sanitized.substring(0, 100);
    }
    
    return sanitized;
  }
}

/// 📄 File Information
class FileInfo {
  final String path;
  final String name;
  final String extension;
  final int size;
  final FileType type;
  final DateTime createdAt;
  final DateTime modifiedAt;

  const FileInfo({
    required this.path,
    required this.name,
    required this.extension,
    required this.size,
    required this.type,
    required this.createdAt,
    required this.modifiedAt,
  });

  String get formattedSize {
    if (size >= 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    } else if (size >= 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else if (size >= 1024) {
      return '${(size / 1024).toStringAsFixed(2)} KB';
    } else {
      return '$size B';
    }
  }

  String get fullName => '$name$extension';
}

/// 📁 File Type Enum
enum FileType { video, audio, thumbnail }

/// 💾 Storage Information
class StorageInfo {
  final int totalSize;
  final int videoSize;
  final int audioSize;
  final int thumbnailSize;
  final int videoCount;
  final int audioCount;
  final int thumbnailCount;

  const StorageInfo({
    required this.totalSize,
    required this.videoSize,
    required this.audioSize,
    required this.thumbnailSize,
    required this.videoCount,
    required this.audioCount,
    required this.thumbnailCount,
  });

  String get formattedTotalSize {
    if (totalSize >= 1024 * 1024 * 1024) {
      return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    } else if (totalSize >= 1024 * 1024) {
      return '${(totalSize / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(totalSize / 1024).toStringAsFixed(2)} KB';
    }
  }

  String get formattedVideoSize {
    if (videoSize >= 1024 * 1024 * 1024) {
      return '${(videoSize / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    } else if (videoSize >= 1024 * 1024) {
      return '${(videoSize / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(videoSize / 1024).toStringAsFixed(2)} KB';
    }
  }

  String get formattedAudioSize {
    if (audioSize >= 1024 * 1024 * 1024) {
      return '${(audioSize / (1024 * 1024 * 1024)).toStringAsFixed(2)} GB';
    } else if (audioSize >= 1024 * 1024) {
      return '${(audioSize / (1024 * 1024)).toStringAsFixed(2)} MB';
    } else {
      return '${(audioSize / 1024).toStringAsFixed(2)} KB';
    }
  }

  int get totalFileCount => videoCount + audioCount + thumbnailCount;
}
