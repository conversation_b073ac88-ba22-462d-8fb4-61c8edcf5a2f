import 'package:equatable/equatable.dart';

/// 🌐 Platform Information Entity
/// Contains information about supported video platforms
class PlatformInfo extends Equatable {
  final String id;
  final String name;
  final String displayName;
  final String iconUrl;
  final List<String> supportedDomains;
  final List<String> supportedFormats;
  final List<String> supportedQualities;
  final bool supportsAudioExtraction;
  final bool supportsWatermarkRemoval;
  final bool supportsPlaylistDownload;
  final bool supportsLiveStreams;
  final bool requiresAuth;
  final Map<String, dynamic>? extractorConfig;
  final PlatformLimitations? limitations;

  const PlatformInfo({
    required this.id,
    required this.name,
    required this.displayName,
    required this.iconUrl,
    required this.supportedDomains,
    required this.supportedFormats,
    required this.supportedQualities,
    this.supportsAudioExtraction = true,
    this.supportsWatermarkRemoval = false,
    this.supportsPlaylistDownload = false,
    this.supportsLiveStreams = false,
    this.requiresAuth = false,
    this.extractorConfig,
    this.limitations,
  });

  /// Check if URL belongs to this platform
  bool isUrlSupported(String url) {
    final lowerUrl = url.toLowerCase();
    return supportedDomains.any((domain) => lowerUrl.contains(domain));
  }

  /// Check if quality is supported
  bool isQualitySupported(String quality) {
    return supportedQualities.contains(quality);
  }

  /// Check if format is supported
  bool isFormatSupported(String format) {
    return supportedFormats.contains(format);
  }

  /// Get platform capabilities summary
  String get capabilitiesSummary {
    final capabilities = <String>[];
    
    if (supportsAudioExtraction) capabilities.add('Audio');
    if (supportsWatermarkRemoval) capabilities.add('Watermark Removal');
    if (supportsPlaylistDownload) capabilities.add('Playlists');
    if (supportsLiveStreams) capabilities.add('Live Streams');
    
    return capabilities.join(', ');
  }

  @override
  List<Object?> get props => [
        id,
        name,
        displayName,
        iconUrl,
        supportedDomains,
        supportedFormats,
        supportedQualities,
        supportsAudioExtraction,
        supportsWatermarkRemoval,
        supportsPlaylistDownload,
        supportsLiveStreams,
        requiresAuth,
        extractorConfig,
        limitations,
      ];

  @override
  String toString() => 'PlatformInfo(id: $id, name: $name, domains: ${supportedDomains.length})';
}

/// 🚫 Platform Limitations
class PlatformLimitations extends Equatable {
  final int? maxVideoLength; // in seconds
  final int? maxFileSize; // in bytes
  final int? maxConcurrentDownloads;
  final Duration? rateLimitDelay;
  final List<String>? restrictedRegions;
  final bool requiresPremium;
  final String? premiumMessage;

  const PlatformLimitations({
    this.maxVideoLength,
    this.maxFileSize,
    this.maxConcurrentDownloads,
    this.rateLimitDelay,
    this.restrictedRegions,
    this.requiresPremium = false,
    this.premiumMessage,
  });

  /// Check if video length is within limits
  bool isVideoLengthAllowed(Duration duration) {
    if (maxVideoLength == null) return true;
    return duration.inSeconds <= maxVideoLength!;
  }

  /// Check if file size is within limits
  bool isFileSizeAllowed(int fileSize) {
    if (maxFileSize == null) return true;
    return fileSize <= maxFileSize!;
  }

  /// Get formatted max video length
  String? get formattedMaxVideoLength {
    if (maxVideoLength == null) return null;
    
    final duration = Duration(seconds: maxVideoLength!);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Get formatted max file size
  String? get formattedMaxFileSize {
    if (maxFileSize == null) return null;
    
    if (maxFileSize! >= 1024 * 1024 * 1024) {
      return '${(maxFileSize! / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    } else if (maxFileSize! >= 1024 * 1024) {
      return '${(maxFileSize! / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(maxFileSize! / 1024).toStringAsFixed(1)}KB';
    }
  }

  @override
  List<Object?> get props => [
        maxVideoLength,
        maxFileSize,
        maxConcurrentDownloads,
        rateLimitDelay,
        restrictedRegions,
        requiresPremium,
        premiumMessage,
      ];
}

/// 🎯 Platform Detection Result
class PlatformDetectionResult extends Equatable {
  final PlatformInfo? platform;
  final bool isSupported;
  final String? reason;
  final Map<String, dynamic>? extractedData;

  const PlatformDetectionResult({
    this.platform,
    required this.isSupported,
    this.reason,
    this.extractedData,
  });

  /// Create successful detection result
  factory PlatformDetectionResult.success(PlatformInfo platform, {Map<String, dynamic>? data}) {
    return PlatformDetectionResult(
      platform: platform,
      isSupported: true,
      extractedData: data,
    );
  }

  /// Create failed detection result
  factory PlatformDetectionResult.failure(String reason) {
    return PlatformDetectionResult(
      isSupported: false,
      reason: reason,
    );
  }

  @override
  List<Object?> get props => [platform, isSupported, reason, extractedData];
}

/// 📊 Platform Statistics
class PlatformStatistics extends Equatable {
  final String platformId;
  final int totalDownloads;
  final int successfulDownloads;
  final int failedDownloads;
  final double averageDownloadTime; // in seconds
  final int totalDataDownloaded; // in bytes
  final DateTime lastUsed;
  final Map<String, int> qualityUsage; // quality -> count
  final Map<String, int> formatUsage; // format -> count

  const PlatformStatistics({
    required this.platformId,
    required this.totalDownloads,
    required this.successfulDownloads,
    required this.failedDownloads,
    required this.averageDownloadTime,
    required this.totalDataDownloaded,
    required this.lastUsed,
    required this.qualityUsage,
    required this.formatUsage,
  });

  /// Get success rate as percentage
  double get successRate {
    if (totalDownloads == 0) return 0.0;
    return (successfulDownloads / totalDownloads) * 100;
  }

  /// Get formatted total data downloaded
  String get formattedTotalData {
    if (totalDataDownloaded >= 1024 * 1024 * 1024) {
      return '${(totalDataDownloaded / (1024 * 1024 * 1024)).toStringAsFixed(2)}GB';
    } else if (totalDataDownloaded >= 1024 * 1024) {
      return '${(totalDataDownloaded / (1024 * 1024)).toStringAsFixed(2)}MB';
    } else {
      return '${(totalDataDownloaded / 1024).toStringAsFixed(2)}KB';
    }
  }

  /// Get formatted average download time
  String get formattedAverageTime {
    final duration = Duration(seconds: averageDownloadTime.round());
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);
    
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get most used quality
  String? get mostUsedQuality {
    if (qualityUsage.isEmpty) return null;
    
    return qualityUsage.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Get most used format
  String? get mostUsedFormat {
    if (formatUsage.isEmpty) return null;
    
    return formatUsage.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  @override
  List<Object?> get props => [
        platformId,
        totalDownloads,
        successfulDownloads,
        failedDownloads,
        averageDownloadTime,
        totalDataDownloaded,
        lastUsed,
        qualityUsage,
        formatUsage,
      ];

  @override
  String toString() => 'PlatformStatistics(platform: $platformId, downloads: $totalDownloads, success: ${successRate.toStringAsFixed(1)}%)';
}

/// 🔧 Platform Configuration
class PlatformConfiguration extends Equatable {
  final String platformId;
  final bool isEnabled;
  final String? preferredQuality;
  final String? preferredFormat;
  final bool enableWatermarkRemoval;
  final bool enableAudioExtraction;
  final int maxConcurrentDownloads;
  final Duration retryDelay;
  final int maxRetryAttempts;
  final Map<String, dynamic>? customSettings;

  const PlatformConfiguration({
    required this.platformId,
    this.isEnabled = true,
    this.preferredQuality,
    this.preferredFormat,
    this.enableWatermarkRemoval = false,
    this.enableAudioExtraction = true,
    this.maxConcurrentDownloads = 2,
    this.retryDelay = const Duration(seconds: 5),
    this.maxRetryAttempts = 3,
    this.customSettings,
  });

  /// Create copy with updated values
  PlatformConfiguration copyWith({
    String? platformId,
    bool? isEnabled,
    String? preferredQuality,
    String? preferredFormat,
    bool? enableWatermarkRemoval,
    bool? enableAudioExtraction,
    int? maxConcurrentDownloads,
    Duration? retryDelay,
    int? maxRetryAttempts,
    Map<String, dynamic>? customSettings,
  }) {
    return PlatformConfiguration(
      platformId: platformId ?? this.platformId,
      isEnabled: isEnabled ?? this.isEnabled,
      preferredQuality: preferredQuality ?? this.preferredQuality,
      preferredFormat: preferredFormat ?? this.preferredFormat,
      enableWatermarkRemoval: enableWatermarkRemoval ?? this.enableWatermarkRemoval,
      enableAudioExtraction: enableAudioExtraction ?? this.enableAudioExtraction,
      maxConcurrentDownloads: maxConcurrentDownloads ?? this.maxConcurrentDownloads,
      retryDelay: retryDelay ?? this.retryDelay,
      maxRetryAttempts: maxRetryAttempts ?? this.maxRetryAttempts,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  @override
  List<Object?> get props => [
        platformId,
        isEnabled,
        preferredQuality,
        preferredFormat,
        enableWatermarkRemoval,
        enableAudioExtraction,
        maxConcurrentDownloads,
        retryDelay,
        maxRetryAttempts,
        customSettings,
      ];

  @override
  String toString() => 'PlatformConfiguration(platform: $platformId, enabled: $isEnabled)';
}
