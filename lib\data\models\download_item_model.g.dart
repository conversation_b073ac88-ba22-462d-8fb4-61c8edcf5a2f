// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'download_item_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class DownloadItemModelAdapter extends TypeAdapter<DownloadItemModel> {
  @override
  final int typeId = 2;

  @override
  DownloadItemModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return DownloadItemModel(
      id: fields[0] as String,
      videoId: fields[1] as String,
      title: fields[2] as String,
      url: fields[3] as String,
      downloadUrl: fields[4] as String,
      thumbnailUrl: fields[5] as String,
      platform: fields[6] as String,
      quality: fields[7] as String,
      format: fields[8] as String,
      filePath: fields[9] as String,
      status: fields[10] as String,
      progress: fields[11] as double,
      createdAt: fields[12] as DateTime,
      startedAt: fields[13] as DateTime?,
      completedAt: fields[14] as DateTime?,
      totalBytes: fields[15] as int?,
      downloadedBytes: fields[16] as int?,
      errorMessage: fields[17] as String?,
      retryCount: fields[18] as int?,
      downloadSpeed: fields[19] as double?,
      estimatedTimeRemaining: fields[20] as Duration?,
    );
  }

  @override
  void write(BinaryWriter writer, DownloadItemModel obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.videoId)
      ..writeByte(2)
      ..write(obj.title)
      ..writeByte(3)
      ..write(obj.url)
      ..writeByte(4)
      ..write(obj.downloadUrl)
      ..writeByte(5)
      ..write(obj.thumbnailUrl)
      ..writeByte(6)
      ..write(obj.platform)
      ..writeByte(7)
      ..write(obj.quality)
      ..writeByte(8)
      ..write(obj.format)
      ..writeByte(9)
      ..write(obj.filePath)
      ..writeByte(10)
      ..write(obj.status)
      ..writeByte(11)
      ..write(obj.progress)
      ..writeByte(12)
      ..write(obj.createdAt)
      ..writeByte(13)
      ..write(obj.startedAt)
      ..writeByte(14)
      ..write(obj.completedAt)
      ..writeByte(15)
      ..write(obj.totalBytes)
      ..writeByte(16)
      ..write(obj.downloadedBytes)
      ..writeByte(17)
      ..write(obj.errorMessage)
      ..writeByte(18)
      ..write(obj.retryCount)
      ..writeByte(19)
      ..write(obj.downloadSpeed)
      ..writeByte(20)
      ..write(obj.estimatedTimeRemaining);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DownloadItemModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DownloadItemModelImpl _$$DownloadItemModelImplFromJson(
        Map<String, dynamic> json) =>
    _$DownloadItemModelImpl(
      id: json['id'] as String,
      videoId: json['videoId'] as String,
      title: json['title'] as String,
      url: json['url'] as String,
      downloadUrl: json['downloadUrl'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String,
      platform: json['platform'] as String,
      quality: json['quality'] as String,
      format: json['format'] as String,
      filePath: json['filePath'] as String,
      status: json['status'] as String,
      progress: (json['progress'] as num).toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      startedAt: json['startedAt'] == null
          ? null
          : DateTime.parse(json['startedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
      totalBytes: (json['totalBytes'] as num?)?.toInt(),
      downloadedBytes: (json['downloadedBytes'] as num?)?.toInt(),
      errorMessage: json['errorMessage'] as String?,
      retryCount: (json['retryCount'] as num?)?.toInt(),
      downloadSpeed: (json['downloadSpeed'] as num?)?.toDouble(),
      estimatedTimeRemaining: json['estimatedTimeRemaining'] == null
          ? null
          : Duration(
              microseconds: (json['estimatedTimeRemaining'] as num).toInt()),
    );

Map<String, dynamic> _$$DownloadItemModelImplToJson(
        _$DownloadItemModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'videoId': instance.videoId,
      'title': instance.title,
      'url': instance.url,
      'downloadUrl': instance.downloadUrl,
      'thumbnailUrl': instance.thumbnailUrl,
      'platform': instance.platform,
      'quality': instance.quality,
      'format': instance.format,
      'filePath': instance.filePath,
      'status': instance.status,
      'progress': instance.progress,
      'createdAt': instance.createdAt.toIso8601String(),
      'startedAt': instance.startedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'totalBytes': instance.totalBytes,
      'downloadedBytes': instance.downloadedBytes,
      'errorMessage': instance.errorMessage,
      'retryCount': instance.retryCount,
      'downloadSpeed': instance.downloadSpeed,
      'estimatedTimeRemaining': instance.estimatedTimeRemaining?.inMicroseconds,
    };
