import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/download_item.dart';
import '../../../domain/entities/video_info.dart';
import '../../../domain/entities/audio_info.dart';

/// 📥 Advanced Download Manager Service
/// Handles concurrent downloads with progress tracking and resume capability
@LazySingleton()
class DownloadManagerService {
  final Dio _dio;
  final Map<String, DownloadTask> _activeTasks = {};
  final Map<String, CancelToken> _cancelTokens = {};
  final StreamController<DownloadProgress> _progressController = StreamController.broadcast();
  final int _maxConcurrentDownloads = 3;

  DownloadManagerService(this._dio);

  /// Stream of download progress updates
  Stream<DownloadProgress> get progressStream => _progressController.stream;

  /// Get active downloads count
  int get activeDownloadsCount => _activeTasks.length;

  /// Check if download limit is reached
  bool get isDownloadLimitReached => _activeTasks.length >= _maxConcurrentDownloads;

  /// Start video download
  Future<String> downloadVideo({
    required VideoInfo videoInfo,
    required VideoQuality quality,
    String? customPath,
    Map<String, String>? headers,
  }) async {
    if (isDownloadLimitReached) {
      throw ConcurrentDownloadLimitException();
    }

    final downloadId = 'video_${DateTime.now().millisecondsSinceEpoch}';
    final fileName = _generateFileName(videoInfo.title, quality.quality, quality.format);
    final filePath = await _getDownloadPath(fileName, customPath, isVideo: true);

    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: videoInfo.id,
      title: videoInfo.title,
      url: videoInfo.url,
      downloadUrl: quality.downloadUrl,
      thumbnailUrl: videoInfo.thumbnailUrl ?? '',
      platform: videoInfo.platform,
      quality: quality.quality,
      format: quality.format,
      filePath: filePath,
      status: DownloadStatus.pending,
      progress: 0.0,
      createdAt: DateTime.now(),
      totalBytes: quality.fileSize,
    );

    await _startDownload(downloadItem, headers);
    return downloadId;
  }

  /// Start audio download
  Future<String> downloadAudio({
    required AudioInfo audioInfo,
    required AudioFormat format,
    String? customPath,
    Map<String, String>? headers,
  }) async {
    if (isDownloadLimitReached) {
      throw ConcurrentDownloadLimitException();
    }

    final downloadId = 'audio_${DateTime.now().millisecondsSinceEpoch}';
    final fileName = _generateFileName(audioInfo.title, '${format.bitrate}kbps', format.format);
    final filePath = await _getDownloadPath(fileName, customPath, isVideo: false);

    final downloadItem = DownloadItem(
      id: downloadId,
      videoId: audioInfo.id,
      title: audioInfo.title,
      url: audioInfo.sourceUrl,
      downloadUrl: format.downloadUrl,
      thumbnailUrl: audioInfo.thumbnailUrl ?? '',
      platform: audioInfo.platform,
      quality: '${format.bitrate}kbps',
      format: format.format,
      filePath: filePath,
      status: DownloadStatus.pending,
      progress: 0.0,
      createdAt: DateTime.now(),
      totalBytes: format.fileSize,
    );

    await _startDownload(downloadItem, headers);
    return downloadId;
  }

  /// Start download task
  Future<void> _startDownload(DownloadItem item, Map<String, String>? headers) async {
    final cancelToken = CancelToken();
    _cancelTokens[item.id] = cancelToken;

    final task = DownloadTask(
      item: item,
      cancelToken: cancelToken,
      startTime: DateTime.now(),
    );

    _activeTasks[item.id] = task;
    _notifyProgress(item.id, DownloadStatus.downloading, 0.0);

    try {
      await _performDownload(task, headers);
    } catch (e) {
      await _handleDownloadError(task, e);
    }
  }

  /// Perform the actual download
  Future<void> _performDownload(DownloadTask task, Map<String, String>? headers) async {
    final file = File(task.item.filePath);
    await file.parent.create(recursive: true);

    int downloadedBytes = 0;
    
    // Check if file exists for resume
    if (await file.exists()) {
      downloadedBytes = await file.length();
      if (downloadedBytes == task.item.totalBytes) {
        // File already complete
        _notifyProgress(task.item.id, DownloadStatus.completed, 1.0);
        _activeTasks.remove(task.item.id);
        _cancelTokens.remove(task.item.id);
        return;
      }
    }

    final options = Options(
      headers: {
        if (headers != null) ...headers,
        if (downloadedBytes > 0) 'Range': 'bytes=$downloadedBytes-',
      },
      responseType: ResponseType.stream,
    );

    final response = await _dio.get(
      task.item.downloadUrl,
      options: options,
      cancelToken: task.cancelToken,
    );

    final stream = response.data.stream as Stream<List<int>>;
    final sink = file.openWrite(mode: downloadedBytes > 0 ? FileMode.append : FileMode.write);

    final totalBytes = task.item.totalBytes ?? 0;
    int receivedBytes = downloadedBytes;
    DateTime lastUpdate = DateTime.now();

    await for (final chunk in stream) {
      if (task.cancelToken.isCancelled) {
        await sink.close();
        throw DownloadCancelledException();
      }

      await sink.add(chunk);
      receivedBytes += chunk.length;

      // Update progress every 100ms
      final now = DateTime.now();
      if (now.difference(lastUpdate).inMilliseconds >= 100) {
        final progress = totalBytes > 0 ? receivedBytes / totalBytes : 0.0;
        final speed = _calculateSpeed(receivedBytes - downloadedBytes, task.startTime);
        final eta = _calculateETA(receivedBytes, totalBytes, speed);

        _notifyProgress(
          task.item.id,
          DownloadStatus.downloading,
          progress,
          downloadSpeed: speed,
          eta: eta,
          downloadedBytes: receivedBytes,
        );

        lastUpdate = now;
      }
    }

    await sink.close();

    // Verify download completion
    final finalSize = await file.length();
    if (totalBytes > 0 && finalSize != totalBytes) {
      throw DownloadInterruptedException(reason: 'File size mismatch');
    }

    _notifyProgress(task.item.id, DownloadStatus.completed, 1.0);
    _activeTasks.remove(task.item.id);
    _cancelTokens.remove(task.item.id);
  }

  /// Handle download errors
  Future<void> _handleDownloadError(DownloadTask task, dynamic error) async {
    String errorMessage;
    
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.receiveTimeout:
          errorMessage = 'Download timeout';
          break;
        case DioExceptionType.cancel:
          errorMessage = 'Download cancelled';
          break;
        case DioExceptionType.connectionError:
          errorMessage = 'Connection error';
          break;
        default:
          errorMessage = 'Network error: ${error.message}';
      }
    } else if (error is DownloadCancelledException) {
      errorMessage = 'Download cancelled by user';
      _notifyProgress(task.item.id, DownloadStatus.cancelled, task.item.progress);
    } else {
      errorMessage = error.toString();
    }

    _notifyProgress(
      task.item.id,
      DownloadStatus.failed,
      task.item.progress,
      errorMessage: errorMessage,
    );

    _activeTasks.remove(task.item.id);
    _cancelTokens.remove(task.item.id);
  }

  /// Pause download
  Future<void> pauseDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('Paused by user');
      _notifyProgress(downloadId, DownloadStatus.paused, _activeTasks[downloadId]?.item.progress ?? 0.0);
    }
  }

  /// Resume download
  Future<void> resumeDownload(DownloadItem item) async {
    if (_activeTasks.containsKey(item.id)) {
      return; // Already downloading
    }

    final updatedItem = item.copyWith(
      status: DownloadStatus.pending,
      startedAt: DateTime.now(),
    );

    await _startDownload(updatedItem, null);
  }

  /// Cancel download
  Future<void> cancelDownload(String downloadId) async {
    final cancelToken = _cancelTokens[downloadId];
    if (cancelToken != null && !cancelToken.isCancelled) {
      cancelToken.cancel('Cancelled by user');
    }

    _activeTasks.remove(downloadId);
    _cancelTokens.remove(downloadId);
  }

  /// Cancel all downloads
  Future<void> cancelAllDownloads() async {
    final downloadIds = List<String>.from(_activeTasks.keys);
    
    for (final id in downloadIds) {
      await cancelDownload(id);
    }
  }

  /// Get download progress
  DownloadProgress? getDownloadProgress(String downloadId) {
    final task = _activeTasks[downloadId];
    if (task == null) return null;

    return DownloadProgress(
      downloadId: downloadId,
      status: task.item.status,
      progress: task.item.progress,
      downloadSpeed: task.item.downloadSpeed,
      eta: task.item.estimatedTimeRemaining,
      downloadedBytes: task.item.downloadedBytes,
      totalBytes: task.item.totalBytes,
    );
  }

  /// Generate safe file name
  String _generateFileName(String title, String quality, String format) {
    // Remove invalid characters
    final cleanTitle = title.replaceAll(RegExp(r'[^\w\s-]'), '').trim();
    final truncatedTitle = cleanTitle.length > 100 ? cleanTitle.substring(0, 100) : cleanTitle;
    
    return '${truncatedTitle}_${quality}.$format';
  }

  /// Get download path
  Future<String> _getDownloadPath(String fileName, String? customPath, {required bool isVideo}) async {
    String basePath;
    
    if (customPath != null) {
      basePath = customPath;
    } else {
      final directory = await getExternalStorageDirectory();
      basePath = path.join(
        directory!.path,
        'VideoDownloader',
        isVideo ? 'Videos' : 'Audio',
      );
    }

    return path.join(basePath, fileName);
  }

  /// Calculate download speed in bytes per second
  double _calculateSpeed(int downloadedBytes, DateTime startTime) {
    final elapsed = DateTime.now().difference(startTime).inMilliseconds;
    if (elapsed <= 0) return 0.0;
    
    return (downloadedBytes * 1000) / elapsed; // bytes per second
  }

  /// Calculate estimated time remaining
  Duration? _calculateETA(int downloadedBytes, int totalBytes, double speed) {
    if (speed <= 0 || totalBytes <= 0 || downloadedBytes >= totalBytes) {
      return null;
    }

    final remainingBytes = totalBytes - downloadedBytes;
    final etaSeconds = remainingBytes / speed;
    
    return Duration(seconds: etaSeconds.round());
  }

  /// Notify progress update
  void _notifyProgress(
    String downloadId,
    DownloadStatus status,
    double progress, {
    double? downloadSpeed,
    Duration? eta,
    int? downloadedBytes,
    String? errorMessage,
  }) {
    final progressUpdate = DownloadProgress(
      downloadId: downloadId,
      status: status,
      progress: progress,
      downloadSpeed: downloadSpeed,
      eta: eta,
      downloadedBytes: downloadedBytes,
      errorMessage: errorMessage,
    );

    _progressController.add(progressUpdate);
  }

  /// Dispose resources
  void dispose() {
    cancelAllDownloads();
    _progressController.close();
  }
}

/// 📥 Download Task
class DownloadTask {
  final DownloadItem item;
  final CancelToken cancelToken;
  final DateTime startTime;

  DownloadTask({
    required this.item,
    required this.cancelToken,
    required this.startTime,
  });
}

/// 📊 Download Progress
class DownloadProgress {
  final String downloadId;
  final DownloadStatus status;
  final double progress;
  final double? downloadSpeed;
  final Duration? eta;
  final int? downloadedBytes;
  final int? totalBytes;
  final String? errorMessage;

  DownloadProgress({
    required this.downloadId,
    required this.status,
    required this.progress,
    this.downloadSpeed,
    this.eta,
    this.downloadedBytes,
    this.totalBytes,
    this.errorMessage,
  });

  /// Get formatted download speed
  String get formattedSpeed {
    if (downloadSpeed == null) return 'Unknown';
    
    if (downloadSpeed! >= 1024 * 1024) {
      return '${(downloadSpeed! / (1024 * 1024)).toStringAsFixed(1)} MB/s';
    } else if (downloadSpeed! >= 1024) {
      return '${(downloadSpeed! / 1024).toStringAsFixed(1)} KB/s';
    } else {
      return '${downloadSpeed!.toStringAsFixed(0)} B/s';
    }
  }

  /// Get formatted ETA
  String get formattedETA {
    if (eta == null) return 'Unknown';
    
    final hours = eta!.inHours;
    final minutes = eta!.inMinutes.remainder(60);
    final seconds = eta!.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m ${seconds}s';
    } else if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }

  /// Get formatted progress percentage
  String get formattedProgress {
    return '${(progress * 100).toStringAsFixed(1)}%';
  }

  @override
  String toString() {
    return 'DownloadProgress(id: $downloadId, status: $status, progress: $formattedProgress)';
  }
}
