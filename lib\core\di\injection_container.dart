import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart' as path;

import 'injection_container.config.dart';

/// 🏗️ Dependency Injection Container
final GetIt getIt = GetIt.instance;

/// 🚀 Initialize all dependencies
@InjectableInit()
Future<void> configureDependencies() async {
  await _initExternalDependencies();
  getIt.init();
  print('✅ All dependencies configured successfully');
}

/// 🔧 Initialize external dependencies that need async setup
Future<void> _initExternalDependencies() async {
  try {
    // Shared Preferences
    final sharedPreferences = await SharedPreferences.getInstance();
    getIt.registerLazySingleton<SharedPreferences>(() => sharedPreferences);

    // Database
    final database = await _initDatabase();
    getIt.registerLazySingleton<Database>(() => database);

    // Connectivity
    getIt.registerLazySingleton<Connectivity>(() => Connectivity());

    // Dio HTTP Client
    final dio = _createDioClient();
    getIt.registerLazySingleton<Dio>(() => dio);

    print('✅ External dependencies initialized');
  } catch (e) {
    print('❌ Failed to initialize external dependencies: $e');
    rethrow;
  }
}

/// 🗄️ Initialize Database
Future<Database> _initDatabase() async {
  final databasesPath = await getDatabasesPath();
  final dbPath = path.join(databasesPath, 'video_downloader.db');

  return await openDatabase(
    dbPath,
    version: 1,
    onCreate: (db, version) async {
      // Downloads table
      await db.execute('''
        CREATE TABLE downloads (
          id TEXT PRIMARY KEY,
          video_id TEXT NOT NULL,
          title TEXT NOT NULL,
          url TEXT NOT NULL,
          thumbnail_url TEXT,
          platform TEXT NOT NULL,
          quality TEXT NOT NULL,
          format TEXT NOT NULL,
          file_path TEXT,
          file_size INTEGER,
          status TEXT NOT NULL,
          progress REAL DEFAULT 0.0,
          download_speed TEXT,
          eta TEXT,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL,
          completed_at INTEGER
        )
      ''');

      // Video info cache table
      await db.execute('''
        CREATE TABLE video_cache (
          id TEXT PRIMARY KEY,
          url TEXT UNIQUE NOT NULL,
          title TEXT NOT NULL,
          thumbnail_url TEXT,
          platform TEXT NOT NULL,
          duration INTEGER,
          view_count INTEGER,
          like_count INTEGER,
          description TEXT,
          uploader TEXT,
          upload_date TEXT,
          qualities TEXT NOT NULL,
          cached_at INTEGER NOT NULL,
          expires_at INTEGER NOT NULL
        )
      ''');

      // Settings table
      await db.execute('''
        CREATE TABLE settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // Download history table
      await db.execute('''
        CREATE TABLE download_history (
          id TEXT PRIMARY KEY,
          video_id TEXT NOT NULL,
          title TEXT NOT NULL,
          url TEXT NOT NULL,
          platform TEXT NOT NULL,
          quality TEXT NOT NULL,
          format TEXT NOT NULL,
          file_size INTEGER,
          download_time INTEGER NOT NULL,
          created_at INTEGER NOT NULL
        )
      ''');

      print('✅ Database tables created successfully');
    },
    onUpgrade: (db, oldVersion, newVersion) async {
      print('🔄 Database upgrade from $oldVersion to $newVersion');
      // Handle database migrations here
    },
  );
}

/// 🌐 Create Dio HTTP Client with interceptors
Dio _createDioClient() {
  final dio = Dio(BaseOptions(
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 60),
    sendTimeout: const Duration(seconds: 30),
    headers: {
      'User-Agent': 'VideoDownloaderPro/2.0.0 (Android)',
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  ));

  // Add logging interceptor
  dio.interceptors.add(LogInterceptor(
    requestBody: true,
    responseBody: true,
    requestHeader: true,
    responseHeader: false,
    error: true,
    logPrint: (obj) => print(obj.toString()),
  ));

  // Add retry interceptor
  dio.interceptors.add(InterceptorsWrapper(
    onError: (error, handler) async {
      if (error.response?.statusCode == 429) {
        // Rate limiting - wait and retry
        await Future.delayed(const Duration(seconds: 2));
        try {
          final response = await dio.fetch(error.requestOptions);
          handler.resolve(response);
        } catch (e) {
          handler.next(error);
        }
      } else {
        handler.next(error);
      }
    },
  ));

  return dio;
}

/// Reset all dependencies (useful for testing)
Future<void> resetDependencies() async {
  try {
    final database = getIt.get<Database>();
    await database.close();

    await getIt.reset();
    print('✅ Dependencies cleaned up successfully');
  } catch (e) {
    print('❌ Failed to cleanup dependencies: $e');
  }
}
