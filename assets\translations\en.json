{"app": {"name": "Video Downloader Pro", "description": "Professional video downloader with advanced features", "version": "2.0.0"}, "common": {"ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "share": "Share", "copy": "Copy", "paste": "Paste", "clear": "Clear", "refresh": "Refresh", "retry": "Retry", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "done": "Done", "continue": "Continue", "skip": "<PERSON><PERSON>", "search": "Search", "filter": "Filter", "sort": "Sort", "settings": "Settings", "help": "Help", "about": "About"}, "navigation": {"home": "Home", "downloads": "Downloads", "files": "Files", "settings": "Settings", "history": "History"}, "home": {"title": "Video Downloader Pro", "subtitle": "Download videos from any platform", "url_input": {"label": "Enter Video URL", "hint": "Paste your video URL here...", "error_empty": "Please enter a valid URL", "error_invalid": "Please enter a valid video URL"}, "quick_actions": {"title": "Quick Actions", "batch_download": "Batch Download", "batch_download_desc": "Download multiple videos", "playlist_download": "Playlist Download", "playlist_download_desc": "Download entire playlists", "audio_extraction": "Audio Only", "audio_extraction_desc": "Extract audio tracks", "scheduled_download": "Schedule Download", "scheduled_download_desc": "Download at specific time"}, "supported_platforms": {"title": "Supported Platforms", "youtube": "YouTube", "tiktok": "TikTok", "instagram": "Instagram", "facebook": "Facebook", "twitter": "Twitter", "vimeo": "Vimeo"}, "statistics": {"title": "Statistics", "total_downloads": "Total Downloads", "active_downloads": "Active Downloads", "completed_today": "Completed Today", "data_saved": "Data Saved"}, "recent_downloads": {"title": "Recent Downloads", "view_all": "View All", "empty_state": "No downloads yet", "empty_desc": "Start downloading videos to see them here"}}, "download": {"title": "Download", "extract_video": "Extract Video", "extracting": "Extracting...", "download_video": "Download Video", "download_audio": "Download Audio", "downloading": "Downloading...", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled", "paused": "Paused", "pending": "Pending", "preparing": "Preparing", "quality": {"title": "Quality", "144p": "144p", "240p": "240p", "360p": "360p", "480p": "480p", "720p": "720p (HD)", "1080p": "1080p (Full HD)", "1440p": "1440p (2K)", "2160p": "2160p (4K)"}, "format": {"title": "Format", "mp4": "MP4", "webm": "WebM", "avi": "AVI", "mkv": "MKV", "mov": "MOV", "mp3": "MP3", "aac": "AAC", "flac": "FLAC", "wav": "WAV", "ogg": "OGG"}, "options": {"title": "Download Options", "watermark_removal": "Remove Watermark", "audio_only": "Audio Only", "custom_path": "Custom Path", "high_priority": "High Priority"}, "progress": {"speed": "Speed", "eta": "ETA", "downloaded": "Downloaded", "total": "Total"}, "actions": {"pause": "Pause", "resume": "Resume", "cancel": "Cancel", "open": "Open", "share": "Share", "delete": "Delete"}}, "files": {"title": "Files", "videos": "Videos", "audio": "Audio", "thumbnails": "Thumbnails", "all_files": "All Files", "sort_by": {"title": "Sort by", "name": "Name", "date": "Date", "size": "Size", "type": "Type"}, "filter_by": {"title": "Filter by", "platform": "Platform", "quality": "Quality", "format": "Format"}, "actions": {"select_all": "Select All", "deselect_all": "Deselect All", "delete_selected": "Delete Selected", "share_selected": "Share Selected", "move_to": "Move to", "copy_to": "Copy to"}, "storage": {"title": "Storage Usage", "total_used": "Total Used", "videos_size": "Videos", "audio_size": "Audio", "thumbnails_size": "Thumbnails", "free_space": "Free Space", "cleanup": "Cleanup"}}, "settings": {"title": "Settings", "general": {"title": "General", "language": "Language", "theme": "Theme", "theme_light": "Light", "theme_dark": "Dark", "theme_system": "System"}, "download": {"title": "Download Settings", "default_quality": "Default Quality", "default_format": "Default Format", "download_path": "Download Path", "max_concurrent": "Max Concurrent Downloads", "auto_retry": "Auto Retry Failed Downloads", "retry_attempts": "Retry Attempts"}, "network": {"title": "Network", "wifi_only": "WiFi Only", "mobile_data_warning": "Mobile Data Warning", "timeout": "Connection Timeout", "proxy": "Proxy Settings"}, "notifications": {"title": "Notifications", "download_progress": "Download Progress", "download_complete": "Download Complete", "download_failed": "Download Failed", "sound": "Sound", "vibration": "Vibration"}, "privacy": {"title": "Privacy & Security", "analytics": "Analytics", "crash_reports": "Crash Reports", "clear_history": "Clear History", "clear_cache": "<PERSON>ache"}, "advanced": {"title": "Advanced", "ffmpeg_settings": "FFmpeg Settings", "debug_mode": "Debug Mode", "experimental_features": "Experimental Features"}, "about": {"title": "About", "version": "Version", "build": "Build", "developer": "Developer", "website": "Website", "support": "Support", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "licenses": "Open Source Licenses"}}, "errors": {"network": {"no_internet": "No internet connection", "timeout": "Connection timeout", "server_error": "Server error"}, "download": {"failed": "Download failed", "cancelled": "Download cancelled", "insufficient_storage": "Insufficient storage space", "file_not_found": "File not found", "permission_denied": "Permission denied"}, "platform": {"unsupported": "Platform not supported", "video_not_found": "Video not found", "private_video": "Video is private", "region_blocked": "Video blocked in your region", "age_restricted": "Video is age-restricted"}, "general": {"unknown": "Unknown error occurred", "try_again": "Please try again", "contact_support": "Contact support if problem persists"}}, "messages": {"download_started": "Download started", "download_completed": "Download completed successfully", "download_failed": "Download failed", "url_copied": "URL copied to clipboard", "file_shared": "File shared successfully", "file_deleted": "File deleted successfully", "settings_saved": "Setting<PERSON> saved", "cache_cleared": "<PERSON><PERSON> cleared", "history_cleared": "History cleared"}, "dialogs": {"confirm_delete": {"title": "Confirm Delete", "message": "Are you sure you want to delete this file?", "multiple": "Are you sure you want to delete {count} files?"}, "confirm_clear": {"title": "Confirm Clear", "cache": "Are you sure you want to clear cache?", "history": "Are you sure you want to clear history?"}, "permission_required": {"title": "Permission Required", "storage": "Storage permission is required to download files", "notification": "Notification permission is required for download updates"}, "quality_selection": {"title": "Select Quality", "video": "Video Quality", "audio": "Audio Quality"}}, "tooltips": {"paste_url": "Paste URL from clipboard", "clear_url": "Clear URL input", "download_options": "Download options", "watermark_removal": "Remove watermarks from video", "high_priority": "Download with high priority", "audio_only": "Extract audio only"}}