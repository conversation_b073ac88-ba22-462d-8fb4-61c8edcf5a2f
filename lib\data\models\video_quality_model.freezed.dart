// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'video_quality_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VideoQualityModel _$VideoQualityModelFromJson(Map<String, dynamic> json) {
  return _VideoQualityModel.fromJson(json);
}

/// @nodoc
mixin _$VideoQualityModel {
  @HiveField(0)
  String get quality => throw _privateConstructorUsedError;
  @HiveField(1)
  String get format => throw _privateConstructorUsedError;
  @HiveField(2)
  String get downloadUrl => throw _privateConstructorUsedError;
  @HiveField(3)
  int? get fileSize => throw _privateConstructorUsedError;
  @HiveField(4)
  bool get hasAudio => throw _privateConstructorUsedError;
  @HiveField(5)
  bool get hasVideo => throw _privateConstructorUsedError;
  @HiveField(6)
  String? get codec => throw _privateConstructorUsedError;
  @HiveField(7)
  int? get bitrate => throw _privateConstructorUsedError;
  @HiveField(8)
  String? get resolution => throw _privateConstructorUsedError;
  @HiveField(9)
  double? get fps => throw _privateConstructorUsedError;
  @HiveField(10)
  bool? get isHdr => throw _privateConstructorUsedError;

  /// Serializes this VideoQualityModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VideoQualityModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VideoQualityModelCopyWith<VideoQualityModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VideoQualityModelCopyWith<$Res> {
  factory $VideoQualityModelCopyWith(
          VideoQualityModel value, $Res Function(VideoQualityModel) then) =
      _$VideoQualityModelCopyWithImpl<$Res, VideoQualityModel>;
  @useResult
  $Res call(
      {@HiveField(0) String quality,
      @HiveField(1) String format,
      @HiveField(2) String downloadUrl,
      @HiveField(3) int? fileSize,
      @HiveField(4) bool hasAudio,
      @HiveField(5) bool hasVideo,
      @HiveField(6) String? codec,
      @HiveField(7) int? bitrate,
      @HiveField(8) String? resolution,
      @HiveField(9) double? fps,
      @HiveField(10) bool? isHdr});
}

/// @nodoc
class _$VideoQualityModelCopyWithImpl<$Res, $Val extends VideoQualityModel>
    implements $VideoQualityModelCopyWith<$Res> {
  _$VideoQualityModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VideoQualityModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quality = null,
    Object? format = null,
    Object? downloadUrl = null,
    Object? fileSize = freezed,
    Object? hasAudio = null,
    Object? hasVideo = null,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? fps = freezed,
    Object? isHdr = freezed,
  }) {
    return _then(_value.copyWith(
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: freezed == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int?,
      hasAudio: null == hasAudio
          ? _value.hasAudio
          : hasAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      hasVideo: null == hasVideo
          ? _value.hasVideo
          : hasVideo // ignore: cast_nullable_to_non_nullable
              as bool,
      codec: freezed == codec
          ? _value.codec
          : codec // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _value.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      resolution: freezed == resolution
          ? _value.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String?,
      fps: freezed == fps
          ? _value.fps
          : fps // ignore: cast_nullable_to_non_nullable
              as double?,
      isHdr: freezed == isHdr
          ? _value.isHdr
          : isHdr // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VideoQualityModelImplCopyWith<$Res>
    implements $VideoQualityModelCopyWith<$Res> {
  factory _$$VideoQualityModelImplCopyWith(_$VideoQualityModelImpl value,
          $Res Function(_$VideoQualityModelImpl) then) =
      __$$VideoQualityModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String quality,
      @HiveField(1) String format,
      @HiveField(2) String downloadUrl,
      @HiveField(3) int? fileSize,
      @HiveField(4) bool hasAudio,
      @HiveField(5) bool hasVideo,
      @HiveField(6) String? codec,
      @HiveField(7) int? bitrate,
      @HiveField(8) String? resolution,
      @HiveField(9) double? fps,
      @HiveField(10) bool? isHdr});
}

/// @nodoc
class __$$VideoQualityModelImplCopyWithImpl<$Res>
    extends _$VideoQualityModelCopyWithImpl<$Res, _$VideoQualityModelImpl>
    implements _$$VideoQualityModelImplCopyWith<$Res> {
  __$$VideoQualityModelImplCopyWithImpl(_$VideoQualityModelImpl _value,
      $Res Function(_$VideoQualityModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of VideoQualityModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? quality = null,
    Object? format = null,
    Object? downloadUrl = null,
    Object? fileSize = freezed,
    Object? hasAudio = null,
    Object? hasVideo = null,
    Object? codec = freezed,
    Object? bitrate = freezed,
    Object? resolution = freezed,
    Object? fps = freezed,
    Object? isHdr = freezed,
  }) {
    return _then(_$VideoQualityModelImpl(
      quality: null == quality
          ? _value.quality
          : quality // ignore: cast_nullable_to_non_nullable
              as String,
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as String,
      downloadUrl: null == downloadUrl
          ? _value.downloadUrl
          : downloadUrl // ignore: cast_nullable_to_non_nullable
              as String,
      fileSize: freezed == fileSize
          ? _value.fileSize
          : fileSize // ignore: cast_nullable_to_non_nullable
              as int?,
      hasAudio: null == hasAudio
          ? _value.hasAudio
          : hasAudio // ignore: cast_nullable_to_non_nullable
              as bool,
      hasVideo: null == hasVideo
          ? _value.hasVideo
          : hasVideo // ignore: cast_nullable_to_non_nullable
              as bool,
      codec: freezed == codec
          ? _value.codec
          : codec // ignore: cast_nullable_to_non_nullable
              as String?,
      bitrate: freezed == bitrate
          ? _value.bitrate
          : bitrate // ignore: cast_nullable_to_non_nullable
              as int?,
      resolution: freezed == resolution
          ? _value.resolution
          : resolution // ignore: cast_nullable_to_non_nullable
              as String?,
      fps: freezed == fps
          ? _value.fps
          : fps // ignore: cast_nullable_to_non_nullable
              as double?,
      isHdr: freezed == isHdr
          ? _value.isHdr
          : isHdr // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VideoQualityModelImpl implements _VideoQualityModel {
  const _$VideoQualityModelImpl(
      {@HiveField(0) required this.quality,
      @HiveField(1) required this.format,
      @HiveField(2) required this.downloadUrl,
      @HiveField(3) this.fileSize,
      @HiveField(4) required this.hasAudio,
      @HiveField(5) required this.hasVideo,
      @HiveField(6) this.codec,
      @HiveField(7) this.bitrate,
      @HiveField(8) this.resolution,
      @HiveField(9) this.fps,
      @HiveField(10) this.isHdr});

  factory _$VideoQualityModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$VideoQualityModelImplFromJson(json);

  @override
  @HiveField(0)
  final String quality;
  @override
  @HiveField(1)
  final String format;
  @override
  @HiveField(2)
  final String downloadUrl;
  @override
  @HiveField(3)
  final int? fileSize;
  @override
  @HiveField(4)
  final bool hasAudio;
  @override
  @HiveField(5)
  final bool hasVideo;
  @override
  @HiveField(6)
  final String? codec;
  @override
  @HiveField(7)
  final int? bitrate;
  @override
  @HiveField(8)
  final String? resolution;
  @override
  @HiveField(9)
  final double? fps;
  @override
  @HiveField(10)
  final bool? isHdr;

  @override
  String toString() {
    return 'VideoQualityModel(quality: $quality, format: $format, downloadUrl: $downloadUrl, fileSize: $fileSize, hasAudio: $hasAudio, hasVideo: $hasVideo, codec: $codec, bitrate: $bitrate, resolution: $resolution, fps: $fps, isHdr: $isHdr)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VideoQualityModelImpl &&
            (identical(other.quality, quality) || other.quality == quality) &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.downloadUrl, downloadUrl) ||
                other.downloadUrl == downloadUrl) &&
            (identical(other.fileSize, fileSize) ||
                other.fileSize == fileSize) &&
            (identical(other.hasAudio, hasAudio) ||
                other.hasAudio == hasAudio) &&
            (identical(other.hasVideo, hasVideo) ||
                other.hasVideo == hasVideo) &&
            (identical(other.codec, codec) || other.codec == codec) &&
            (identical(other.bitrate, bitrate) || other.bitrate == bitrate) &&
            (identical(other.resolution, resolution) ||
                other.resolution == resolution) &&
            (identical(other.fps, fps) || other.fps == fps) &&
            (identical(other.isHdr, isHdr) || other.isHdr == isHdr));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, quality, format, downloadUrl,
      fileSize, hasAudio, hasVideo, codec, bitrate, resolution, fps, isHdr);

  /// Create a copy of VideoQualityModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VideoQualityModelImplCopyWith<_$VideoQualityModelImpl> get copyWith =>
      __$$VideoQualityModelImplCopyWithImpl<_$VideoQualityModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VideoQualityModelImplToJson(
      this,
    );
  }
}

abstract class _VideoQualityModel implements VideoQualityModel {
  const factory _VideoQualityModel(
      {@HiveField(0) required final String quality,
      @HiveField(1) required final String format,
      @HiveField(2) required final String downloadUrl,
      @HiveField(3) final int? fileSize,
      @HiveField(4) required final bool hasAudio,
      @HiveField(5) required final bool hasVideo,
      @HiveField(6) final String? codec,
      @HiveField(7) final int? bitrate,
      @HiveField(8) final String? resolution,
      @HiveField(9) final double? fps,
      @HiveField(10) final bool? isHdr}) = _$VideoQualityModelImpl;

  factory _VideoQualityModel.fromJson(Map<String, dynamic> json) =
      _$VideoQualityModelImpl.fromJson;

  @override
  @HiveField(0)
  String get quality;
  @override
  @HiveField(1)
  String get format;
  @override
  @HiveField(2)
  String get downloadUrl;
  @override
  @HiveField(3)
  int? get fileSize;
  @override
  @HiveField(4)
  bool get hasAudio;
  @override
  @HiveField(5)
  bool get hasVideo;
  @override
  @HiveField(6)
  String? get codec;
  @override
  @HiveField(7)
  int? get bitrate;
  @override
  @HiveField(8)
  String? get resolution;
  @override
  @HiveField(9)
  double? get fps;
  @override
  @HiveField(10)
  bool? get isHdr;

  /// Create a copy of VideoQualityModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VideoQualityModelImplCopyWith<_$VideoQualityModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
