name: download_videos
description: "A professional video downloader app with watermark removal support"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.8

  # HTTP and networking
  dio: ^5.3.2

  # Local Storage
  path_provider: ^2.1.1

  # Permissions
  permission_handler: ^11.0.1

  # UI and media
  video_player: ^2.7.2
  cached_network_image: ^3.3.0

  # Utilities
  intl: ^0.19.0
  path: ^1.8.3
  url_launcher: ^6.2.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
