name: download_videos
description: "🎬 Professional Video Downloader Pro - Download videos & audio from all platforms with watermark removal"
publish_to: 'none'

version: 2.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # Core dependencies
  cupertino_icons: ^1.0.8

  # State Management & Architecture
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  get_it: ^7.6.4
  injectable: ^2.3.2

  # HTTP and networking
  dio: ^5.3.2
  connectivity_plus: ^5.0.1
  http: ^1.1.0

  # Local Storage & Database
  path_provider: ^2.1.1
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Permissions & Device
  permission_handler: ^11.0.1
  device_info_plus: ^9.1.0
  package_info_plus: ^4.2.0

  # UI and media
  video_player: ^2.7.2
  cached_network_image: ^3.3.0
  flutter_svg: ^2.0.7
  lottie: ^2.7.0
  shimmer: ^3.0.0
  photo_view: ^0.14.0

  # Audio & Video Processing
  ffmpeg_kit_flutter: ^6.0.3
  audio_waveforms: ^1.0.5
  video_thumbnail: ^0.5.3

  # File Management
  file_picker: ^6.1.1
  open_file: ^3.3.2
  share_plus: ^7.2.1

  # Utilities
  intl: ^0.19.0
  path: ^1.8.3
  url_launcher: ^6.2.1
  uuid: ^4.1.0
  crypto: ^3.0.3
  mime: ^1.0.4

  # UI Enhancements
  flutter_staggered_animations: ^1.1.1
  flutter_slidable: ^3.0.0
  pull_to_refresh: ^2.0.0
  flutter_speed_dial: ^7.0.0
  animated_bottom_navigation_bar: ^1.3.0

  # Localization
  easy_localization: ^3.0.3

  # Analytics & Crash Reporting
  firebase_core: ^2.17.0
  firebase_analytics: ^10.5.1
  firebase_crashlytics: ^3.4.1

  # Background Tasks
  workmanager: ^0.5.1
  flutter_local_notifications: ^16.1.0

  # Code Generation
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  injectable_generator: ^2.4.1
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1
  freezed: ^2.4.6
  freezed_annotation: ^2.4.1
  logger: ^2.0.2+1
  mockito: ^5.4.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/animations/
    - assets/translations/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
        - asset: assets/fonts/Poppins-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
