import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:injectable/injectable.dart';

/// 🌐 Network Information Interface
abstract class NetworkInfo {
  Future<bool> get isConnected;
  Future<bool> get isConnectedToWifi;
  Future<bool> get isConnectedToMobile;
  Future<ConnectivityResult> get connectionType;
  Stream<ConnectivityResult> get onConnectivityChanged;
}

/// 🌐 Network Information Implementation
@LazySingleton(as: NetworkInfo)
class NetworkInfoImpl implements NetworkInfo {
  final Connectivity _connectivity;

  NetworkInfoImpl(this._connectivity);

  @override
  Future<bool> get isConnected async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  @override
  Future<bool> get isConnectedToWifi async {
    final result = await _connectivity.checkConnectivity();
    return result == ConnectivityResult.wifi;
  }

  @override
  Future<bool> get isConnectedToMobile async {
    final result = await _connectivity.checkConnectivity();
    return result == ConnectivityResult.mobile;
  }

  @override
  Future<ConnectivityResult> get connectionType async {
    return await _connectivity.checkConnectivity();
  }

  @override
  Stream<ConnectivityResult> get onConnectivityChanged {
    return _connectivity.onConnectivityChanged;
  }
}

/// 🌐 Network Status Enum
enum NetworkStatus {
  connected,
  disconnected,
  wifi,
  mobile,
  ethernet,
  bluetooth,
  vpn,
  other,
}

/// 🌐 Network Speed Enum
enum NetworkSpeed {
  slow,      // < 1 Mbps
  moderate,  // 1-5 Mbps
  fast,      // 5-25 Mbps
  veryFast,  // > 25 Mbps
}

/// 🌐 Network Quality Information
class NetworkQuality {
  final NetworkSpeed speed;
  final double bandwidth; // in Mbps
  final int latency; // in milliseconds
  final bool isStable;

  const NetworkQuality({
    required this.speed,
    required this.bandwidth,
    required this.latency,
    required this.isStable,
  });

  bool get isGoodForDownload => speed.index >= NetworkSpeed.moderate.index && isStable;
  bool get isGoodForStreaming => speed.index >= NetworkSpeed.fast.index && isStable;
  bool get isGoodForHDDownload => speed.index >= NetworkSpeed.fast.index && isStable;

  @override
  String toString() {
    return 'NetworkQuality(speed: $speed, bandwidth: ${bandwidth}Mbps, latency: ${latency}ms, stable: $isStable)';
  }
}

/// 🌐 Extended Network Information
@LazySingleton()
class ExtendedNetworkInfo {
  final NetworkInfo _networkInfo;
  
  ExtendedNetworkInfo(this._networkInfo);

  /// Check if network is suitable for downloading
  Future<bool> isSuitableForDownload() async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return false;

    final connectionType = await _networkInfo.connectionType;
    
    // Always allow WiFi downloads
    if (connectionType == ConnectivityResult.wifi) {
      return true;
    }
    
    // For mobile, check user preferences (this would come from settings)
    if (connectionType == ConnectivityResult.mobile) {
      // This should be checked against user settings
      // For now, we'll allow mobile downloads
      return true;
    }
    
    return false;
  }

  /// Check if network is suitable for high quality downloads
  Future<bool> isSuitableForHDDownload() async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return false;

    final connectionType = await _networkInfo.connectionType;
    
    // Prefer WiFi for HD downloads
    if (connectionType == ConnectivityResult.wifi) {
      return true;
    }
    
    // Allow mobile HD downloads only if user explicitly allows it
    if (connectionType == ConnectivityResult.mobile) {
      // This should be checked against user settings
      // For now, we'll be conservative and not allow HD on mobile
      return false;
    }
    
    return false;
  }

  /// Get recommended download quality based on network
  Future<String> getRecommendedQuality() async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return '360p';

    final connectionType = await _networkInfo.connectionType;
    
    switch (connectionType) {
      case ConnectivityResult.wifi:
        return '1080p'; // High quality for WiFi
      case ConnectivityResult.mobile:
        return '720p'; // Medium quality for mobile
      case ConnectivityResult.ethernet:
        return '1080p'; // High quality for ethernet
      default:
        return '480p'; // Conservative quality for other connections
    }
  }

  /// Get network status description
  Future<String> getNetworkStatusDescription() async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return 'No internet connection';

    final connectionType = await _networkInfo.connectionType;
    
    switch (connectionType) {
      case ConnectivityResult.wifi:
        return 'Connected via WiFi';
      case ConnectivityResult.mobile:
        return 'Connected via Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Connected via Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Connected via Bluetooth';
      case ConnectivityResult.vpn:
        return 'Connected via VPN';
      case ConnectivityResult.other:
        return 'Connected via Other';
      case ConnectivityResult.none:
        return 'No internet connection';
    }
  }

  /// Monitor network changes
  Stream<NetworkStatus> monitorNetworkChanges() {
    return _networkInfo.onConnectivityChanged.map((result) {
      switch (result) {
        case ConnectivityResult.wifi:
          return NetworkStatus.wifi;
        case ConnectivityResult.mobile:
          return NetworkStatus.mobile;
        case ConnectivityResult.ethernet:
          return NetworkStatus.connected;
        case ConnectivityResult.bluetooth:
          return NetworkStatus.bluetooth;
        case ConnectivityResult.vpn:
          return NetworkStatus.vpn;
        case ConnectivityResult.other:
          return NetworkStatus.other;
        case ConnectivityResult.none:
          return NetworkStatus.disconnected;
      }
    });
  }

  /// Check if current network allows background downloads
  Future<bool> allowsBackgroundDownloads() async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return false;

    final connectionType = await _networkInfo.connectionType;
    
    // Allow background downloads on WiFi and Ethernet
    return connectionType == ConnectivityResult.wifi || 
           connectionType == ConnectivityResult.ethernet;
  }

  /// Estimate download time based on file size and network speed
  Future<Duration> estimateDownloadTime(int fileSizeBytes) async {
    final isConnected = await _networkInfo.isConnected;
    if (!isConnected) return const Duration(hours: 24); // Infinite time

    final connectionType = await _networkInfo.connectionType;
    
    // Estimated speeds in bytes per second
    int estimatedSpeed;
    switch (connectionType) {
      case ConnectivityResult.wifi:
        estimatedSpeed = 5 * 1024 * 1024; // 5 MB/s
        break;
      case ConnectivityResult.mobile:
        estimatedSpeed = 1 * 1024 * 1024; // 1 MB/s
        break;
      case ConnectivityResult.ethernet:
        estimatedSpeed = 10 * 1024 * 1024; // 10 MB/s
        break;
      default:
        estimatedSpeed = 500 * 1024; // 500 KB/s
    }

    final timeInSeconds = fileSizeBytes / estimatedSpeed;
    return Duration(seconds: timeInSeconds.round());
  }
}
