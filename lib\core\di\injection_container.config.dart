// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:connectivity_plus/connectivity_plus.dart' as _i895;
import 'package:dio/dio.dart' as _i361;
import 'package:download_videos/core/network/network_info.dart' as _i710;
import 'package:download_videos/core/services/background_service.dart' as _i776;
import 'package:download_videos/core/utils/logger.dart' as _i62;
import 'package:download_videos/data/datasources/local/download_manager_service.dart'
    as _i777;
import 'package:download_videos/data/datasources/local/watermark_removal_service.dart'
    as _i174;
import 'package:download_videos/data/datasources/remote/video_extractor_service.dart'
    as _i620;
import 'package:download_videos/data/services/advanced_download_service.dart'
    as _i570;
import 'package:download_videos/data/services/file_management_service.dart'
    as _i707;
import 'package:download_videos/domain/repositories/download_repository.dart'
    as _i514;
import 'package:download_videos/domain/repositories/video_repository.dart'
    as _i304;
import 'package:download_videos/domain/usecases/extract_video_info.dart'
    as _i728;
import 'package:download_videos/domain/usecases/get_downloads.dart' as _i763;
import 'package:download_videos/domain/usecases/start_download.dart' as _i895;
import 'package:download_videos/presentation/blocs/video_extraction/video_extraction_bloc.dart'
    as _i806;
import 'package:flutter_local_notifications/flutter_local_notifications.dart'
    as _i163;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.singleton<_i62.AppLogger>(() => _i62.AppLogger());
    gh.lazySingleton<_i174.WatermarkRemovalService>(
        () => _i174.WatermarkRemovalService());
    gh.lazySingleton<_i707.FileManagementService>(
        () => _i707.FileManagementService());
    gh.singleton<_i776.BackgroundService>(
        () => _i776.BackgroundService(gh<_i62.AppLogger>()));
    gh.factory<_i728.ExtractVideoInfo>(
        () => _i728.ExtractVideoInfo(gh<_i304.VideoRepository>()));
    gh.lazySingleton<_i777.DownloadManagerService>(
        () => _i777.DownloadManagerService(gh<_i361.Dio>()));
    gh.lazySingleton<_i620.VideoExtractorService>(
        () => _i620.VideoExtractorService(gh<_i361.Dio>()));
    gh.factory<_i806.VideoExtractionBloc>(() => _i806.VideoExtractionBloc(
          gh<_i728.ExtractVideoInfo>(),
          gh<_i62.AppLogger>(),
        ));
    gh.factory<_i763.GetDownloads>(
        () => _i763.GetDownloads(gh<_i514.DownloadRepository>()));
    gh.factory<_i895.StartDownload>(
        () => _i895.StartDownload(gh<_i514.DownloadRepository>()));
    gh.lazySingleton<_i570.AdvancedDownloadService>(
        () => _i570.AdvancedDownloadService(
              gh<_i361.Dio>(),
              gh<_i163.FlutterLocalNotificationsPlugin>(),
            ));
    gh.lazySingleton<_i710.NetworkInfo>(
        () => _i710.NetworkInfoImpl(gh<_i895.Connectivity>()));
    gh.lazySingleton<_i710.ExtendedNetworkInfo>(
        () => _i710.ExtendedNetworkInfo(gh<_i710.NetworkInfo>()));
    return this;
  }
}
