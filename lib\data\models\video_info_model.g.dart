// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'video_info_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class VideoInfoModelAdapter extends TypeAdapter<VideoInfoModel> {
  @override
  final int typeId = 0;

  @override
  VideoInfoModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VideoInfoModel(
      id: fields[0] as String,
      title: fields[1] as String,
      url: fields[2] as String,
      thumbnailUrl: fields[3] as String,
      platform: fields[4] as String,
      durationInSeconds: fields[5] as int,
      availableQualities: (fields[6] as List).cast<VideoQualityModel>(),
      description: fields[7] as String?,
      uploader: fields[8] as String?,
      uploadDate: fields[9] as DateTime?,
      viewCount: fields[10] as int?,
      likeCount: fields[11] as int?,
      tags: (fields[12] as List?)?.cast<String>(),
      hasWatermark: fields[13] as bool?,
      extractedAt: fields[14] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, VideoInfoModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.url)
      ..writeByte(3)
      ..write(obj.thumbnailUrl)
      ..writeByte(4)
      ..write(obj.platform)
      ..writeByte(5)
      ..write(obj.durationInSeconds)
      ..writeByte(6)
      ..write(obj.availableQualities)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.uploader)
      ..writeByte(9)
      ..write(obj.uploadDate)
      ..writeByte(10)
      ..write(obj.viewCount)
      ..writeByte(11)
      ..write(obj.likeCount)
      ..writeByte(12)
      ..write(obj.tags)
      ..writeByte(13)
      ..write(obj.hasWatermark)
      ..writeByte(14)
      ..write(obj.extractedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VideoInfoModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VideoInfoModelImpl _$$VideoInfoModelImplFromJson(Map<String, dynamic> json) =>
    _$VideoInfoModelImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      url: json['url'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String,
      platform: json['platform'] as String,
      durationInSeconds: (json['durationInSeconds'] as num).toInt(),
      availableQualities: (json['availableQualities'] as List<dynamic>)
          .map((e) => VideoQualityModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      description: json['description'] as String?,
      uploader: json['uploader'] as String?,
      uploadDate: json['uploadDate'] == null
          ? null
          : DateTime.parse(json['uploadDate'] as String),
      viewCount: (json['viewCount'] as num?)?.toInt(),
      likeCount: (json['likeCount'] as num?)?.toInt(),
      tags: (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList(),
      hasWatermark: json['hasWatermark'] as bool?,
      extractedAt: json['extractedAt'] == null
          ? null
          : DateTime.parse(json['extractedAt'] as String),
    );

Map<String, dynamic> _$$VideoInfoModelImplToJson(
        _$VideoInfoModelImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'url': instance.url,
      'thumbnailUrl': instance.thumbnailUrl,
      'platform': instance.platform,
      'durationInSeconds': instance.durationInSeconds,
      'availableQualities': instance.availableQualities,
      'description': instance.description,
      'uploader': instance.uploader,
      'uploadDate': instance.uploadDate?.toIso8601String(),
      'viewCount': instance.viewCount,
      'likeCount': instance.likeCount,
      'tags': instance.tags,
      'hasWatermark': instance.hasWatermark,
      'extractedAt': instance.extractedAt?.toIso8601String(),
    };
