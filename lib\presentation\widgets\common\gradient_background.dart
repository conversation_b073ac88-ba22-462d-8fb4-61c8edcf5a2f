import 'package:flutter/material.dart';

/// 🌈 Gradient Background Widget
/// Provides a beautiful animated gradient background
class GradientBackground extends StatefulWidget {
  final Widget child;
  final List<Color>? colors;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;
  final bool animated;
  final Duration animationDuration;

  const GradientBackground({
    super.key,
    required this.child,
    this.colors,
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
    this.animated = true,
    this.animationDuration = const Duration(seconds: 10),
  });

  @override
  State<GradientBackground> createState() => _GradientBackgroundState();
}

class _GradientBackgroundState extends State<GradientBackground>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    if (widget.animated) {
      _animationController = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      _animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));

      _animationController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    if (widget.animated) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final defaultColors = widget.colors ?? _getDefaultColors(theme, isDark);

    if (!widget.animated) {
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: widget.begin,
            end: widget.end,
            colors: defaultColors,
          ),
        ),
        child: widget.child,
      );
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.lerp(
                widget.begin,
                widget.end,
                _animation.value,
              )!,
              end: Alignment.lerp(
                widget.end,
                widget.begin,
                _animation.value,
              )!,
              colors: _interpolateColors(defaultColors, _animation.value),
            ),
          ),
          child: widget.child,
        );
      },
    );
  }

  List<Color> _getDefaultColors(ThemeData theme, bool isDark) {
    if (isDark) {
      return [
        theme.colorScheme.surface.withOpacity(0.95),
        theme.colorScheme.primary.withOpacity(0.1),
        theme.colorScheme.secondary.withOpacity(0.05),
        theme.colorScheme.surface.withOpacity(0.98),
      ];
    } else {
      return [
        theme.colorScheme.primary.withOpacity(0.05),
        theme.colorScheme.secondary.withOpacity(0.03),
        theme.colorScheme.tertiary.withOpacity(0.02),
        theme.colorScheme.surface.withOpacity(0.99),
      ];
    }
  }

  List<Color> _interpolateColors(List<Color> colors, double t) {
    return colors.map((color) {
      final hsl = HSLColor.fromColor(color);
      final newHue = (hsl.hue + (t * 10)) % 360;
      return hsl.withHue(newHue).toColor();
    }).toList();
  }
}

/// 🌟 Animated Gradient Background
/// More advanced gradient with multiple animation effects
class AnimatedGradientBackground extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final List<List<Color>> gradientSequence;

  const AnimatedGradientBackground({
    super.key,
    required this.child,
    this.duration = const Duration(seconds: 8),
    this.gradientSequence = const [],
  });

  @override
  State<AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _currentGradientIndex = 0;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentGradientIndex = 
              (_currentGradientIndex + 1) % _getGradientSequence().length;
        });
        _controller.reset();
        _controller.forward();
      }
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  List<List<Color>> _getGradientSequence() {
    if (widget.gradientSequence.isNotEmpty) {
      return widget.gradientSequence;
    }

    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (isDark) {
      return [
        [
          const Color(0xFF1A1A2E),
          const Color(0xFF16213E),
          const Color(0xFF0F3460),
        ],
        [
          const Color(0xFF16213E),
          const Color(0xFF0F3460),
          const Color(0xFF533483),
        ],
        [
          const Color(0xFF0F3460),
          const Color(0xFF533483),
          const Color(0xFF1A1A2E),
        ],
      ];
    } else {
      return [
        [
          const Color(0xFFF8F9FA),
          const Color(0xFFE3F2FD),
          const Color(0xFFF3E5F5),
        ],
        [
          const Color(0xFFE3F2FD),
          const Color(0xFFF3E5F5),
          const Color(0xFFFFF3E0),
        ],
        [
          const Color(0xFFF3E5F5),
          const Color(0xFFFFF3E0),
          const Color(0xFFF8F9FA),
        ],
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    final gradients = _getGradientSequence();
    final currentGradient = gradients[_currentGradientIndex];
    final nextGradient = gradients[(_currentGradientIndex + 1) % gradients.length];

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: _lerpColorList(currentGradient, nextGradient, _animation.value),
            ),
          ),
          child: widget.child,
        );
      },
    );
  }

  List<Color> _lerpColorList(List<Color> a, List<Color> b, double t) {
    final result = <Color>[];
    final length = a.length > b.length ? a.length : b.length;
    
    for (int i = 0; i < length; i++) {
      final colorA = i < a.length ? a[i] : a.last;
      final colorB = i < b.length ? b[i] : b.last;
      result.add(Color.lerp(colorA, colorB, t)!);
    }
    
    return result;
  }
}

/// ✨ Particle Background
/// Animated particles floating in the background
class ParticleBackground extends StatefulWidget {
  final Widget child;
  final int particleCount;
  final Color particleColor;
  final double particleSize;
  final Duration animationDuration;

  const ParticleBackground({
    super.key,
    required this.child,
    this.particleCount = 50,
    this.particleColor = Colors.white24,
    this.particleSize = 2.0,
    this.animationDuration = const Duration(seconds: 20),
  });

  @override
  State<ParticleBackground> createState() => _ParticleBackgroundState();
}

class _ParticleBackgroundState extends State<ParticleBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _particles = List.generate(
      widget.particleCount,
      (index) => Particle.random(),
    );

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return CustomPaint(
                painter: ParticlePainter(
                  particles: _particles,
                  animation: _controller.value,
                  color: widget.particleColor,
                  size: widget.particleSize,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// 🎨 Particle Painter
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animation;
  final Color color;
  final double size;

  ParticlePainter({
    required this.particles,
    required this.animation,
    required this.color,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      final x = (particle.x + animation * particle.speedX) % 1.0;
      final y = (particle.y + animation * particle.speedY) % 1.0;
      
      final position = Offset(
        x * canvasSize.width,
        y * canvasSize.height,
      );

      canvas.drawCircle(position, size * particle.scale, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// ⭐ Particle Class
class Particle {
  final double x;
  final double y;
  final double speedX;
  final double speedY;
  final double scale;

  Particle({
    required this.x,
    required this.y,
    required this.speedX,
    required this.speedY,
    required this.scale,
  });

  factory Particle.random() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return Particle(
      x: (random % 1000) / 1000.0,
      y: (random % 1000) / 1000.0,
      speedX: ((random % 200) - 100) / 10000.0,
      speedY: ((random % 200) - 100) / 10000.0,
      scale: 0.5 + ((random % 100) / 200.0),
    );
  }
}
