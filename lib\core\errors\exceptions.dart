/// 🚨 Base Exception Class
/// All custom exceptions in the app should extend this class
abstract class AppException implements Exception {
  final String message;
  final int? code;
  final dynamic data;

  const AppException({
    required this.message,
    this.code,
    this.data,
  });

  @override
  String toString() => 'AppException: $message (Code: $code)';
}

/// 🌐 Network Related Exceptions
class NetworkException extends AppException {
  const NetworkException({
    required super.message,
    super.code,
    super.data,
  });
}

class NoInternetException extends NetworkException {
  const NoInternetException() : super(message: 'No internet connection available');
}

class ServerException extends NetworkException {
  const ServerException({
    required super.message,
    super.code,
    super.data,
  });
}

class TimeoutException extends NetworkException {
  const TimeoutException() : super(message: 'Request timeout');
}

class BadRequestException extends NetworkException {
  const BadRequestException({required super.message}) : super(code: 400);
}

class UnauthorizedException extends NetworkException {
  const UnauthorizedException() : super(message: 'Unauthorized access', code: 401);
}

class ForbiddenException extends NetworkException {
  const ForbiddenException() : super(message: 'Access forbidden', code: 403);
}

class NotFoundException extends NetworkException {
  const NotFoundException({required super.message}) : super(code: 404);
}

class InternalServerException extends NetworkException {
  const InternalServerException() : super(message: 'Internal server error', code: 500);
}

/// 📱 Platform Related Exceptions
class PlatformException extends AppException {
  const PlatformException({
    required super.message,
    super.code,
    super.data,
  });
}

class UnsupportedPlatformException extends PlatformException {
  const UnsupportedPlatformException({required String platform})
      : super(message: 'Platform "$platform" is not supported');
}

class InvalidUrlException extends PlatformException {
  const InvalidUrlException({required String url})
      : super(message: 'Invalid URL: "$url"');
}

class VideoNotFoundException extends PlatformException {
  const VideoNotFoundException()
      : super(message: 'Video not found or may be private/deleted');
}

class VideoUnavailableException extends PlatformException {
  const VideoUnavailableException({required String reason})
      : super(message: 'Video unavailable: $reason');
}

class RegionBlockedException extends PlatformException {
  const RegionBlockedException()
      : super(message: 'Video is blocked in your region');
}

class AgeRestrictedException extends PlatformException {
  const AgeRestrictedException()
      : super(message: 'Video is age-restricted');
}

class PrivateVideoException extends PlatformException {
  const PrivateVideoException()
      : super(message: 'Video is private');
}

/// 💾 Storage Related Exceptions
class StorageException extends AppException {
  const StorageException({
    required super.message,
    super.code,
    super.data,
  });
}

class InsufficientStorageException extends StorageException {
  const InsufficientStorageException()
      : super(message: 'Insufficient storage space');
}

class FileNotFoundException extends StorageException {
  const FileNotFoundException({required String filePath})
      : super(message: 'File not found: $filePath');
}

class FileAccessException extends StorageException {
  const FileAccessException({required String reason})
      : super(message: 'File access denied: $reason');
}

class DirectoryCreationException extends StorageException {
  const DirectoryCreationException({required String path})
      : super(message: 'Failed to create directory: $path');
}

class FileCorruptedException extends StorageException {
  const FileCorruptedException({required String filePath})
      : super(message: 'File is corrupted: $filePath');
}

class DiskFullException extends StorageException {
  const DiskFullException()
      : super(message: 'Disk is full');
}

/// 🔐 Permission Related Exceptions
class PermissionException extends AppException {
  const PermissionException({
    required super.message,
    super.code,
    super.data,
  });
}

class StoragePermissionException extends PermissionException {
  const StoragePermissionException()
      : super(message: 'Storage permission is required');
}

class NetworkPermissionException extends PermissionException {
  const NetworkPermissionException()
      : super(message: 'Network permission is required');
}

class NotificationPermissionException extends PermissionException {
  const NotificationPermissionException()
      : super(message: 'Notification permission is required');
}

class CameraPermissionException extends PermissionException {
  const CameraPermissionException()
      : super(message: 'Camera permission is required');
}

class MicrophonePermissionException extends PermissionException {
  const MicrophonePermissionException()
      : super(message: 'Microphone permission is required');
}

/// 📥 Download Related Exceptions
class DownloadException extends AppException {
  const DownloadException({
    required super.message,
    super.code,
    super.data,
  });
}

class DownloadCancelledException extends DownloadException {
  const DownloadCancelledException()
      : super(message: 'Download was cancelled');
}

class DownloadInterruptedException extends DownloadException {
  const DownloadInterruptedException({required String reason})
      : super(message: 'Download interrupted: $reason');
}

class ExtractionException extends DownloadException {
  const ExtractionException({required String reason})
      : super(message: 'Failed to extract video information: $reason');
}

class QualityNotAvailableException extends DownloadException {
  const QualityNotAvailableException({required String quality})
      : super(message: 'Quality "$quality" is not available');
}

class DownloadLimitExceededException extends DownloadException {
  const DownloadLimitExceededException()
      : super(message: 'Download limit exceeded');
}

class ConcurrentDownloadLimitException extends DownloadException {
  const ConcurrentDownloadLimitException()
      : super(message: 'Maximum concurrent downloads reached');
}

/// 🎵 Audio Related Exceptions
class AudioException extends AppException {
  const AudioException({
    required super.message,
    super.code,
    super.data,
  });
}

class AudioExtractionException extends AudioException {
  const AudioExtractionException({required String reason})
      : super(message: 'Failed to extract audio: $reason');
}

class AudioConversionException extends AudioException {
  const AudioConversionException({required String format})
      : super(message: 'Failed to convert audio to $format');
}

class UnsupportedAudioFormatException extends AudioException {
  const UnsupportedAudioFormatException({required String format})
      : super(message: 'Audio format "$format" is not supported');
}

class AudioCorruptedException extends AudioException {
  const AudioCorruptedException()
      : super(message: 'Audio file is corrupted');
}

/// 🎬 Video Related Exceptions
class VideoException extends AppException {
  const VideoException({
    required super.message,
    super.code,
    super.data,
  });
}

class VideoProcessingException extends VideoException {
  const VideoProcessingException({required String reason})
      : super(message: 'Video processing failed: $reason');
}

class VideoConversionException extends VideoException {
  const VideoConversionException({required String format})
      : super(message: 'Failed to convert video to $format');
}

class UnsupportedVideoFormatException extends VideoException {
  const UnsupportedVideoFormatException({required String format})
      : super(message: 'Video format "$format" is not supported');
}

class VideoCorruptedException extends VideoException {
  const VideoCorruptedException()
      : super(message: 'Video file is corrupted');
}

class WatermarkRemovalException extends VideoException {
  const WatermarkRemovalException({required String reason})
      : super(message: 'Watermark removal failed: $reason');
}

/// 🗄️ Database Related Exceptions
class DatabaseException extends AppException {
  const DatabaseException({
    required super.message,
    super.code,
    super.data,
  });
}

class DatabaseConnectionException extends DatabaseException {
  const DatabaseConnectionException()
      : super(message: 'Failed to connect to database');
}

class DatabaseOperationException extends DatabaseException {
  const DatabaseOperationException({required String operation})
      : super(message: 'Database $operation operation failed');
}

class DatabaseMigrationException extends DatabaseException {
  const DatabaseMigrationException({required String version})
      : super(message: 'Database migration to version $version failed');
}

/// 🔧 Validation Related Exceptions
class ValidationException extends AppException {
  const ValidationException({
    required super.message,
    super.code,
    super.data,
  });
}

class EmptyUrlException extends ValidationException {
  const EmptyUrlException() : super(message: 'URL cannot be empty');
}

class InvalidFormatException extends ValidationException {
  const InvalidFormatException({required String format})
      : super(message: 'Invalid format: $format');
}

class FileSizeExceededException extends ValidationException {
  const FileSizeExceededException({required String maxSize})
      : super(message: 'File size exceeds maximum limit of $maxSize');
}

class InvalidParameterException extends ValidationException {
  const InvalidParameterException({required String parameter})
      : super(message: 'Invalid parameter: $parameter');
}

/// 🔄 Conversion Related Exceptions
class ConversionException extends AppException {
  const ConversionException({
    required super.message,
    super.code,
    super.data,
  });
}

class UnsupportedConversionException extends ConversionException {
  const UnsupportedConversionException({
    required String fromFormat,
    required String toFormat,
  }) : super(message: 'Conversion from $fromFormat to $toFormat is not supported');
}

class ConversionTimeoutException extends ConversionException {
  const ConversionTimeoutException()
      : super(message: 'Conversion process timed out');
}

class FFmpegException extends ConversionException {
  const FFmpegException({required String reason})
      : super(message: 'FFmpeg error: $reason');
}

/// ☁️ Cloud Related Exceptions
class CloudException extends AppException {
  const CloudException({
    required super.message,
    super.code,
    super.data,
  });
}

class CloudUploadException extends CloudException {
  const CloudUploadException({required String provider})
      : super(message: 'Failed to upload to $provider');
}

class CloudAuthenticationException extends CloudException {
  const CloudAuthenticationException({required String provider})
      : super(message: 'Authentication failed for $provider');
}

class CloudQuotaExceededException extends CloudException {
  const CloudQuotaExceededException({required String provider})
      : super(message: 'Storage quota exceeded for $provider');
}

/// 🔧 General Application Exceptions
class UnknownException extends AppException {
  const UnknownException({String? message})
      : super(message: message ?? 'An unknown error occurred');
}

class CacheException extends AppException {
  const CacheException({required super.message});
}

class ConfigurationException extends AppException {
  const ConfigurationException({required super.message});
}

class InitializationException extends AppException {
  const InitializationException({required String component})
      : super(message: 'Failed to initialize $component');
}
