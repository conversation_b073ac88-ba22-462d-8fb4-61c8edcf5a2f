import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 🔗 URL Input Section Widget
/// Professional URL input with validation and smart features
class UrlInputSection extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final Function(String) onSubmitted;
  final VoidCallback onPaste;
  final VoidCallback onClear;
  final bool isLoading;

  const UrlInputSection({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onSubmitted,
    required this.onPaste,
    required this.onClear,
    this.isLoading = false,
  });

  @override
  State<UrlInputSection> createState() => _UrlInputSectionState();
}

class _UrlInputSectionState extends State<UrlInputSection>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  bool _isValid = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupListeners();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  void _setupListeners() {
    widget.controller.addListener(_validateUrl);
  }

  void _validateUrl() {
    final url = widget.controller.text.trim();
    
    if (url.isEmpty) {
      setState(() {
        _isValid = false;
        _errorMessage = null;
      });
      return;
    }

    // URL validation patterns
    final urlPatterns = [
      RegExp(r'(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+'),
      RegExp(r'(https?://)?(www\.)?tiktok\.com/.+'),
      RegExp(r'(https?://)?(www\.)?instagram\.com/.+'),
      RegExp(r'(https?://)?(www\.)?facebook\.com/.+'),
      RegExp(r'(https?://)?(www\.)?(twitter\.com|x\.com)/.+'),
      RegExp(r'(https?://)?(www\.)?vimeo\.com/.+'),
    ];

    bool isValidUrl = false;
    for (final pattern in urlPatterns) {
      if (pattern.hasMatch(url)) {
        isValidUrl = true;
        break;
      }
    }

    setState(() {
      _isValid = isValidUrl;
      _errorMessage = isValidUrl ? null : 'Please enter a valid video URL';
    });

    if (isValidUrl) {
      _pulseController.forward().then((_) => _pulseController.reverse());
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildUrlInput(),
        if (_errorMessage != null) ...[
          const SizedBox(height: 8),
          _buildErrorMessage(),
        ],
        const SizedBox(height: 20),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.link_rounded,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          'Enter Video URL',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const Spacer(),
        if (_isValid)
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Icon(
                  Icons.check_circle_rounded,
                  color: Colors.green,
                  size: 24,
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildUrlInput() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _errorMessage != null
              ? Theme.of(context).colorScheme.error
              : _isValid
                  ? Colors.green
                  : Theme.of(context).colorScheme.outline.withOpacity(0.3),
          width: _isValid || _errorMessage != null ? 2 : 1,
        ),
        boxShadow: [
          if (_isValid)
            BoxShadow(
              color: Colors.green.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: TextField(
        controller: widget.controller,
        focusNode: widget.focusNode,
        decoration: InputDecoration(
          hintText: 'Paste your video URL here...',
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          prefixIcon: Padding(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.video_library_outlined,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          suffixIcon: widget.controller.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    widget.controller.clear();
                    widget.onClear();
                  },
                  icon: Icon(
                    Icons.clear_rounded,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        maxLines: 3,
        minLines: 1,
        keyboardType: TextInputType.url,
        textInputAction: TextInputAction.done,
        onSubmitted: widget.onSubmitted,
        style: Theme.of(context).textTheme.bodyLarge,
      ),
    );
  }

  Widget _buildErrorMessage() {
    return Row(
      children: [
        Icon(
          Icons.error_outline_rounded,
          color: Theme.of(context).colorScheme.error,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          _errorMessage!,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: widget.onPaste,
            icon: const Icon(Icons.content_paste_rounded),
            label: const Text('Paste'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: FilledButton.icon(
            onPressed: _isValid && !widget.isLoading
                ? () => widget.onSubmitted(widget.controller.text.trim())
                : null,
            icon: widget.isLoading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  )
                : const Icon(Icons.download_rounded),
            label: Text(widget.isLoading ? 'Extracting...' : 'Extract Video'),
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

/// 🎯 Smart URL Suggestions Widget
class UrlSuggestions extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onSuggestionTap;

  const UrlSuggestions({
    super.key,
    required this.suggestions,
    required this.onSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(12),
            child: Text(
              'Recent URLs',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          ...suggestions.map((suggestion) => _buildSuggestionItem(
                context,
                suggestion,
              )),
        ],
      ),
    );
  }

  Widget _buildSuggestionItem(BuildContext context, String suggestion) {
    return InkWell(
      onTap: () => onSuggestionTap(suggestion),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            Icon(
              Icons.history_rounded,
              size: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                suggestion,
                style: Theme.of(context).textTheme.bodyMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.arrow_forward_ios_rounded,
              size: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }
}
