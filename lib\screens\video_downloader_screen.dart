import 'package:flutter/material.dart';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import 'dart:math';

class VideoDownloaderScreen extends StatefulWidget {
  const VideoDownloaderScreen({super.key});

  @override
  State<VideoDownloaderScreen> createState() => _VideoDownloaderScreenState();
}

class _VideoDownloaderScreenState extends State<VideoDownloaderScreen>
    with TickerProviderStateMixin {
  final TextEditingController _urlController = TextEditingController();
  final Dio _dio = Dio();

  bool _isExtracting = false;
  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  String _downloadSpeed = '';
  String _downloadStatus = '';

  late AnimationController _progressAnimationController;
  late Animation<double> _progressAnimation;

  VideoInfo? _currentVideo;
  List<DownloadItem> _downloads = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _requestPermissions();
  }

  void _initializeAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _requestPermissions() async {
    await Permission.storage.request();
    await Permission.manageExternalStorage.request();
  }

  @override
  void dispose() {
    _urlController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  Future<void> _extractVideo() async {
    if (_urlController.text.trim().isEmpty) {
      _showSnackBar('Please enter a video URL', isError: true);
      return;
    }

    setState(() {
      _isExtracting = true;
      _downloadStatus = 'Extracting video information...';
    });

    try {
      // Simulate video extraction
      await Future.delayed(const Duration(seconds: 2));

      // Create mock video info
      final videoInfo = VideoInfo(
        id: 'video_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Sample Video - Professional Quality',
        url: _urlController.text,
        thumbnailUrl:
            'https://via.placeholder.com/320x180/6366F1/FFFFFF?text=Video+Thumbnail',
        platform: _detectPlatform(_urlController.text),
        duration: const Duration(minutes: 5, seconds: 30),
        qualities: [
          VideoQuality(
            quality: '1080p',
            format: 'mp4',
            downloadUrl:
                'https://file-examples.com/storage/fe86c86b9c66d0b8d8e2e0e/2017/10/file_example_MP4_1920_18MG.mp4',
            fileSize: 18 * 1024 * 1024, // 18MB
          ),
          VideoQuality(
            quality: '720p',
            format: 'mp4',
            downloadUrl:
                'https://file-examples.com/storage/fe86c86b9c66d0b8d8e2e0e/2017/10/file_example_MP4_1280_10MG.mp4',
            fileSize: 10 * 1024 * 1024, // 10MB
          ),
          VideoQuality(
            quality: '480p',
            format: 'mp4',
            downloadUrl:
                'https://file-examples.com/storage/fe86c86b9c66d0b8d8e2e0e/2017/10/file_example_MP4_480_1_5MG.mp4',
            fileSize: (1.5 * 1024 * 1024).round(), // 1.5MB
          ),
        ],
      );

      setState(() {
        _currentVideo = videoInfo;
        _isExtracting = false;
        _downloadStatus = 'Video extracted successfully!';
      });

      _showSnackBar(
          'Video extracted successfully! Choose quality to download.');
    } catch (e) {
      setState(() {
        _isExtracting = false;
        _downloadStatus = 'Failed to extract video';
      });
      _showSnackBar('Failed to extract video: $e', isError: true);
    }
  }

  String _detectPlatform(String url) {
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      return 'YouTube';
    } else if (url.contains('tiktok.com')) {
      return 'TikTok';
    } else if (url.contains('instagram.com')) {
      return 'Instagram';
    } else if (url.contains('facebook.com')) {
      return 'Facebook';
    } else {
      return 'Unknown';
    }
  }

  Future<void> _downloadVideo(VideoQuality quality) async {
    if (_currentVideo == null) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
      _downloadStatus = 'Starting download...';
    });

    try {
      // Get download directory
      final directory = await getExternalStorageDirectory();
      final downloadDir =
          Directory('${directory!.path}/VideoDownloader/Downloads');
      if (!await downloadDir.exists()) {
        await downloadDir.create(recursive: true);
      }

      final fileName =
          '${_currentVideo!.title.replaceAll(RegExp(r'[^\w\s-]'), '')}_${quality.quality}.${quality.format}';
      final filePath = '${downloadDir.path}/$fileName';

      // Create download item
      final downloadItem = DownloadItem(
        id: 'download_${DateTime.now().millisecondsSinceEpoch}',
        videoId: _currentVideo!.id,
        title: _currentVideo!.title,
        quality: quality.quality,
        format: quality.format,
        filePath: filePath,
        status: DownloadStatus.downloading,
        progress: 0.0,
        createdAt: DateTime.now(),
      );

      setState(() {
        _downloads.add(downloadItem);
      });

      // Start download with progress tracking
      await _dio.download(
        quality.downloadUrl,
        filePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            final progress = received / total;
            final speed = _calculateDownloadSpeed(received);

            setState(() {
              _downloadProgress = progress;
              _downloadSpeed = speed;
              _downloadStatus =
                  'Downloading... ${(progress * 100).toStringAsFixed(1)}%';
            });

            // Update download item
            final index = _downloads.indexWhere((d) => d.id == downloadItem.id);
            if (index != -1) {
              _downloads[index] = _downloads[index].copyWith(
                progress: progress,
                downloadSpeed: _parseSpeed(speed),
              );
            }
          }
        },
      );

      // Update download item as completed
      final index = _downloads.indexWhere((d) => d.id == downloadItem.id);
      if (index != -1) {
        _downloads[index] = _downloads[index].copyWith(
          status: DownloadStatus.completed,
          progress: 1.0,
          completedAt: DateTime.now(),
        );
      }

      setState(() {
        _isDownloading = false;
        _downloadProgress = 1.0;
        _downloadStatus = 'Download completed successfully!';
      });

      _showSnackBar('Video downloaded successfully to: $filePath');
      _progressAnimationController.forward();
    } catch (e) {
      setState(() {
        _isDownloading = false;
        _downloadStatus = 'Download failed';
      });
      _showSnackBar('Download failed: $e', isError: true);
    }
  }

  String _calculateDownloadSpeed(int bytesReceived) {
    // Simple speed calculation (this is a mock implementation)
    final speedBps = bytesReceived / 1024; // KB/s
    if (speedBps > 1024) {
      return '${(speedBps / 1024).toStringAsFixed(1)} MB/s';
    } else {
      return '${speedBps.toStringAsFixed(1)} KB/s';
    }
  }

  double _parseSpeed(String speed) {
    // Parse speed string to double (mock implementation)
    final numStr = speed.replaceAll(RegExp(r'[^\d.]'), '');
    return double.tryParse(numStr) ?? 0.0;
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: isError ? 4 : 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Video Downloader Pro'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          IconButton(
            icon: const Icon(Icons.download_done),
            onPressed: () => _showDownloadsDialog(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeaderSection(),
            const SizedBox(height: 32),
            _buildUrlInputSection(),
            if (_currentVideo != null) ...[
              const SizedBox(height: 32),
              _buildVideoInfoSection(),
            ],
            if (_isDownloading || _downloadProgress > 0) ...[
              const SizedBox(height: 32),
              _buildDownloadProgressSection(),
            ],
            const SizedBox(height: 32),
            _buildFeaturesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_library,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 16),
          const Text(
            'Professional Video Downloader',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'Download videos from YouTube, TikTok, Instagram, and more',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUrlInputSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enter Video URL',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _urlController,
              decoration: InputDecoration(
                hintText: 'Paste your video URL here...',
                prefixIcon: const Icon(Icons.link),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () => _urlController.clear(),
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              maxLines: 3,
              minLines: 1,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isExtracting ? null : _extractVideo,
                icon: _isExtracting
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(Icons.search),
                label: Text(
                  _isExtracting ? 'Extracting...' : 'Extract Video',
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            if (_downloadStatus.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                _downloadStatus,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: _downloadStatus.contains('Failed') ||
                              _downloadStatus.contains('failed')
                          ? Colors.red
                          : Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildVideoInfoSection() {
    if (_currentVideo == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.network(
                    _currentVideo!.thumbnailUrl,
                    width: 80,
                    height: 60,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        width: 80,
                        height: 60,
                        color: Colors.grey[300],
                        child: const Icon(Icons.video_library),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _currentVideo!.title,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_currentVideo!.platform} • ${_currentVideo!.formattedDuration}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              'Available Qualities',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...(_currentVideo!.qualities
                .map((quality) => _buildQualityOption(quality))),
          ],
        ),
      ),
    );
  }

  Widget _buildQualityOption(VideoQuality quality) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: _isDownloading ? null : () => _downloadVideo(quality),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Row(
          children: [
            Icon(Icons.high_quality),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${quality.quality} (${quality.format.toUpperCase()})',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    quality.formattedFileSize,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),
            const Icon(Icons.download),
          ],
        ),
      ),
    );
  }

  Widget _buildDownloadProgressSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Download Progress',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: _downloadProgress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${(_downloadProgress * 100).toStringAsFixed(1)}%',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
                if (_downloadSpeed.isNotEmpty)
                  Text(
                    _downloadSpeed,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeaturesSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Features',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              Icons.high_quality,
              'High Quality Downloads',
              'Download videos in up to 4K resolution',
            ),
            _buildFeatureItem(
              Icons.speed,
              'Fast Downloads',
              'Optimized for maximum download speed',
            ),
            _buildFeatureItem(
              Icons.no_photography,
              'Watermark Removal',
              'Remove watermarks from downloaded videos',
            ),
            _buildFeatureItem(
              Icons.audio_file,
              'Audio Extraction',
              'Extract audio from videos in MP3 format',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.onPrimaryContainer,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withValues(alpha: 0.7),
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showDownloadsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Downloads'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: _downloads.isEmpty
              ? const Center(
                  child: Text('No downloads yet'),
                )
              : ListView.builder(
                  itemCount: _downloads.length,
                  itemBuilder: (context, index) {
                    final download = _downloads[index];
                    return ListTile(
                      leading: Icon(
                        download.status == DownloadStatus.completed
                            ? Icons.check_circle
                            : Icons.download,
                        color: download.status == DownloadStatus.completed
                            ? Colors.green
                            : Colors.blue,
                      ),
                      title: Text(
                        download.title,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Text(
                        '${download.quality} • ${download.statusText}',
                      ),
                      trailing: Text(
                        '${(download.progress * 100).toStringAsFixed(0)}%',
                      ),
                    );
                  },
                ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

// Data Models
class VideoInfo {
  final String id;
  final String title;
  final String url;
  final String thumbnailUrl;
  final String platform;
  final Duration duration;
  final List<VideoQuality> qualities;

  VideoInfo({
    required this.id,
    required this.title,
    required this.url,
    required this.thumbnailUrl,
    required this.platform,
    required this.duration,
    required this.qualities,
  });

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }
}

class VideoQuality {
  final String quality;
  final String format;
  final String downloadUrl;
  final int fileSize;

  VideoQuality({
    required this.quality,
    required this.format,
    required this.downloadUrl,
    required this.fileSize,
  });

  String get formattedFileSize {
    const units = ['B', 'KB', 'MB', 'GB'];
    double size = fileSize.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }
}

enum DownloadStatus {
  pending,
  downloading,
  completed,
  failed,
}

class DownloadItem {
  final String id;
  final String videoId;
  final String title;
  final String quality;
  final String format;
  final String filePath;
  final DownloadStatus status;
  final double progress;
  final DateTime createdAt;
  final DateTime? completedAt;
  final double? downloadSpeed;

  DownloadItem({
    required this.id,
    required this.videoId,
    required this.title,
    required this.quality,
    required this.format,
    required this.filePath,
    required this.status,
    required this.progress,
    required this.createdAt,
    this.completedAt,
    this.downloadSpeed,
  });

  String get statusText {
    switch (status) {
      case DownloadStatus.pending:
        return 'Pending';
      case DownloadStatus.downloading:
        return 'Downloading';
      case DownloadStatus.completed:
        return 'Completed';
      case DownloadStatus.failed:
        return 'Failed';
    }
  }

  DownloadItem copyWith({
    String? id,
    String? videoId,
    String? title,
    String? quality,
    String? format,
    String? filePath,
    DownloadStatus? status,
    double? progress,
    DateTime? createdAt,
    DateTime? completedAt,
    double? downloadSpeed,
  }) {
    return DownloadItem(
      id: id ?? this.id,
      videoId: videoId ?? this.videoId,
      title: title ?? this.title,
      quality: quality ?? this.quality,
      format: format ?? this.format,
      filePath: filePath ?? this.filePath,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      downloadSpeed: downloadSpeed ?? this.downloadSpeed,
    );
  }
}
