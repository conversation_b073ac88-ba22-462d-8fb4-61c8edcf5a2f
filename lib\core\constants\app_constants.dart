/// 🎯 Application Constants
/// Contains all the constant values used throughout the app
class AppConstants {
  // App Information
  static const String appName = 'Video Downloader Pro';
  static const String appVersion = '2.0.0';
  static const String appDescription = 'Professional Video & Audio Downloader';
  
  // Supported Platforms
  static const List<String> supportedPlatforms = [
    'YouTube',
    'TikTok', 
    'Instagram',
    'Facebook',
    'Twitter',
    'Vimeo',
    'Dailymotion',
    'Reddit',
    'Pinterest',
    'LinkedIn'
  ];
  
  // Video Quality Options
  static const List<String> videoQualities = [
    '144p',
    '240p', 
    '360p',
    '480p',
    '720p',
    '1080p',
    '1440p',
    '2160p', // 4K
    '4320p'  // 8K
  ];
  
  // Audio Quality Options
  static const List<String> audioQualities = [
    '64kbps',
    '128kbps',
    '192kbps', 
    '256kbps',
    '320kbps'
  ];
  
  // Supported Video Formats
  static const List<String> videoFormats = [
    'mp4',
    'avi',
    'mkv',
    'mov',
    'wmv',
    'flv',
    'webm',
    'm4v'
  ];
  
  // Supported Audio Formats
  static const List<String> audioFormats = [
    'mp3',
    'aac',
    'flac',
    'ogg',
    'wav',
    'm4a',
    'wma'
  ];
  
  // File Size Limits (in bytes)
  static const int maxVideoFileSize = 2 * 1024 * 1024 * 1024; // 2GB
  static const int maxAudioFileSize = 500 * 1024 * 1024; // 500MB
  
  // Download Settings
  static const int maxConcurrentDownloads = 3;
  static const int downloadTimeoutSeconds = 300; // 5 minutes
  static const int retryAttempts = 3;
  
  // Database
  static const String databaseName = 'video_downloader.db';
  static const int databaseVersion = 1;
  
  // Storage Paths
  static const String videoDownloadPath = '/VideoDownloader/Videos';
  static const String audioDownloadPath = '/VideoDownloader/Audio';
  static const String thumbnailCachePath = '/VideoDownloader/Thumbnails';
  static const String tempPath = '/VideoDownloader/Temp';
  
  // Network
  static const int connectionTimeoutMs = 30000;
  static const int receiveTimeoutMs = 60000;
  static const int sendTimeoutMs = 30000;
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;
  
  // Animation Durations
  static const int shortAnimationMs = 200;
  static const int mediumAnimationMs = 400;
  static const int longAnimationMs = 600;
  
  // Notification
  static const String downloadChannelId = 'download_channel';
  static const String downloadChannelName = 'Download Progress';
  static const String downloadChannelDescription = 'Shows download progress notifications';
  
  // Shared Preferences Keys
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyDownloadQuality = 'download_quality';
  static const String keyDownloadPath = 'download_path';
  static const String keyAutoDownload = 'auto_download';
  static const String keyWifiOnly = 'wifi_only';
  static const String keyNotifications = 'notifications';
  static const String keyFirstLaunch = 'first_launch';
  
  // Error Messages
  static const String errorNoInternet = 'No internet connection';
  static const String errorInvalidUrl = 'Invalid URL provided';
  static const String errorUnsupportedPlatform = 'Platform not supported';
  static const String errorDownloadFailed = 'Download failed';
  static const String errorPermissionDenied = 'Permission denied';
  static const String errorStorageFull = 'Storage space insufficient';
  static const String errorFileNotFound = 'File not found';
  
  // Success Messages
  static const String successDownloadComplete = 'Download completed successfully';
  static const String successVideoExtracted = 'Video information extracted';
  static const String successAudioExtracted = 'Audio extracted successfully';
  
  // Regular Expressions for URL validation
  static const String youtubeUrlPattern = r'(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+';
  static const String tiktokUrlPattern = r'(https?://)?(www\.)?tiktok\.com/.+';
  static const String instagramUrlPattern = r'(https?://)?(www\.)?instagram\.com/.+';
  static const String facebookUrlPattern = r'(https?://)?(www\.)?facebook\.com/.+';
  static const String twitterUrlPattern = r'(https?://)?(www\.)?(twitter\.com|x\.com)/.+';
  static const String vimeoUrlPattern = r'(https?://)?(www\.)?vimeo\.com/.+';
  
  // API Endpoints (if needed)
  static const String baseApiUrl = 'https://api.videodownloader.pro';
  static const String extractEndpoint = '/extract';
  static const String downloadEndpoint = '/download';
  static const String statusEndpoint = '/status';
  
  // Feature Flags
  static const bool enableWatermarkRemoval = true;
  static const bool enableBatchDownload = true;
  static const bool enableScheduledDownload = true;
  static const bool enableCloudBackup = true;
  static const bool enableAnalytics = true;
  
  // Watermark Removal Settings
  static const double watermarkDetectionThreshold = 0.8;
  static const int maxWatermarkRemovalAttempts = 3;
  
  // Batch Download Settings
  static const int maxBatchSize = 50;
  static const int batchProcessingDelay = 1000; // 1 second
  
  // Cloud Storage
  static const List<String> supportedCloudProviders = [
    'Google Drive',
    'Dropbox',
    'OneDrive',
    'iCloud'
  ];
}
