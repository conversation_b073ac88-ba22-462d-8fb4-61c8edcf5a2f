import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../core/errors/exceptions.dart';
import '../../../domain/entities/video_info.dart';
import '../../../domain/entities/platform_info.dart';
import '../../../domain/entities/audio_info.dart';

/// 🎬 Advanced Video Extractor Service
/// Handles video information extraction from multiple platforms
@LazySingleton()
class VideoExtractorService {
  final Dio _dio;
  final Map<String, PlatformExtractor> _extractors = {};

  VideoExtractorService(this._dio) {
    _initializeExtractors();
  }

  /// Initialize platform-specific extractors
  void _initializeExtractors() {
    _extractors['youtube'] = YouTubeExtractor(_dio);
    _extractors['tiktok'] = TikTokExtractor(_dio);
    _extractors['instagram'] = InstagramExtractor(_dio);
    _extractors['facebook'] = FacebookExtractor(_dio);
    _extractors['twitter'] = TwitterExtractor(_dio);
    _extractors['vimeo'] = VimeoExtractor(_dio);
  }

  /// Detect platform from URL
  PlatformDetectionResult detectPlatform(String url) {
    final lowerUrl = url.toLowerCase();
    
    // YouTube
    if (lowerUrl.contains('youtube.com') || lowerUrl.contains('youtu.be')) {
      return PlatformDetectionResult.success(
        PlatformInfo(
          id: 'youtube',
          name: 'YouTube',
          displayName: 'YouTube',
          iconUrl: 'assets/icons/youtube.png',
          supportedDomains: ['youtube.com', 'youtu.be'],
          supportedFormats: ['mp4', 'webm', 'mp3', 'aac'],
          supportedQualities: ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'],
          supportsAudioExtraction: true,
          supportsWatermarkRemoval: false,
          supportsPlaylistDownload: true,
          supportsLiveStreams: true,
        ),
      );
    }
    
    // TikTok
    if (lowerUrl.contains('tiktok.com')) {
      return PlatformDetectionResult.success(
        PlatformInfo(
          id: 'tiktok',
          name: 'TikTok',
          displayName: 'TikTok',
          iconUrl: 'assets/icons/tiktok.png',
          supportedDomains: ['tiktok.com'],
          supportedFormats: ['mp4', 'mp3'],
          supportedQualities: ['360p', '480p', '720p', '1080p'],
          supportsAudioExtraction: true,
          supportsWatermarkRemoval: true,
          supportsPlaylistDownload: false,
          supportsLiveStreams: false,
        ),
      );
    }
    
    // Instagram
    if (lowerUrl.contains('instagram.com')) {
      return PlatformDetectionResult.success(
        PlatformInfo(
          id: 'instagram',
          name: 'Instagram',
          displayName: 'Instagram',
          iconUrl: 'assets/icons/instagram.png',
          supportedDomains: ['instagram.com'],
          supportedFormats: ['mp4', 'mp3'],
          supportedQualities: ['480p', '720p', '1080p'],
          supportsAudioExtraction: true,
          supportsWatermarkRemoval: false,
          supportsPlaylistDownload: false,
          supportsLiveStreams: true,
        ),
      );
    }
    
    return PlatformDetectionResult.failure('Unsupported platform');
  }

  /// Extract video information from URL
  Future<VideoInfo> extractVideoInfo(String url) async {
    final detection = detectPlatform(url);
    
    if (!detection.isSupported || detection.platform == null) {
      throw UnsupportedPlatformException(platform: 'Unknown');
    }
    
    final extractor = _extractors[detection.platform!.id];
    if (extractor == null) {
      throw ExtractionException(reason: 'No extractor available for ${detection.platform!.name}');
    }
    
    try {
      return await extractor.extractVideoInfo(url);
    } catch (e) {
      if (e is AppException) rethrow;
      throw ExtractionException(reason: e.toString());
    }
  }

  /// Extract audio information from URL
  Future<AudioInfo> extractAudioInfo(String url) async {
    final detection = detectPlatform(url);
    
    if (!detection.isSupported || detection.platform == null) {
      throw UnsupportedPlatformException(platform: 'Unknown');
    }
    
    if (!detection.platform!.supportsAudioExtraction) {
      throw AudioExtractionException(reason: '${detection.platform!.name} does not support audio extraction');
    }
    
    final extractor = _extractors[detection.platform!.id];
    if (extractor == null) {
      throw ExtractionException(reason: 'No extractor available for ${detection.platform!.name}');
    }
    
    try {
      return await extractor.extractAudioInfo(url);
    } catch (e) {
      if (e is AppException) rethrow;
      throw AudioExtractionException(reason: e.toString());
    }
  }

  /// Validate URL format
  bool isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  /// Get supported platforms
  List<PlatformInfo> getSupportedPlatforms() {
    return [
      PlatformInfo(
        id: 'youtube',
        name: 'YouTube',
        displayName: 'YouTube',
        iconUrl: 'assets/icons/youtube.png',
        supportedDomains: ['youtube.com', 'youtu.be'],
        supportedFormats: ['mp4', 'webm', 'mp3', 'aac'],
        supportedQualities: ['144p', '240p', '360p', '480p', '720p', '1080p', '1440p', '2160p'],
        supportsAudioExtraction: true,
        supportsPlaylistDownload: true,
        supportsLiveStreams: true,
      ),
      PlatformInfo(
        id: 'tiktok',
        name: 'TikTok',
        displayName: 'TikTok',
        iconUrl: 'assets/icons/tiktok.png',
        supportedDomains: ['tiktok.com'],
        supportedFormats: ['mp4', 'mp3'],
        supportedQualities: ['360p', '480p', '720p', '1080p'],
        supportsAudioExtraction: true,
        supportsWatermarkRemoval: true,
      ),
      PlatformInfo(
        id: 'instagram',
        name: 'Instagram',
        displayName: 'Instagram',
        iconUrl: 'assets/icons/instagram.png',
        supportedDomains: ['instagram.com'],
        supportedFormats: ['mp4', 'mp3'],
        supportedQualities: ['480p', '720p', '1080p'],
        supportsAudioExtraction: true,
        supportsLiveStreams: true,
      ),
    ];
  }
}

/// 🎯 Base Platform Extractor
abstract class PlatformExtractor {
  final Dio dio;
  
  PlatformExtractor(this.dio);
  
  Future<VideoInfo> extractVideoInfo(String url);
  Future<AudioInfo> extractAudioInfo(String url);
}

/// 📺 YouTube Extractor
class YouTubeExtractor extends PlatformExtractor {
  YouTubeExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    try {
      // Extract video ID from URL
      final videoId = _extractVideoId(url);
      if (videoId == null) {
        throw InvalidUrlException(url: url);
      }

      // Mock implementation - in real app, use yt-dlp or YouTube API
      return VideoInfo(
        id: videoId,
        title: 'Sample YouTube Video',
        url: url,
        thumbnailUrl: 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg',
        platform: 'YouTube',
        duration: const Duration(minutes: 5, seconds: 30),
        qualities: [
          const VideoQuality(
            quality: '1080p',
            format: 'mp4',
            downloadUrl: 'https://example.com/video.mp4',
            fileSize: 50 * 1024 * 1024,
            resolution: 1080,
            fps: 30,
          ),
          const VideoQuality(
            quality: '720p',
            format: 'mp4',
            downloadUrl: 'https://example.com/video_720.mp4',
            fileSize: 30 * 1024 * 1024,
            resolution: 720,
            fps: 30,
          ),
        ],
        audioQualities: [
          const AudioQuality(
            format: 'mp3',
            downloadUrl: 'https://example.com/audio.mp3',
            fileSize: 5 * 1024 * 1024,
            bitrate: 320,
            sampleRate: 44100,
            channels: 2,
          ),
        ],
        viewCount: 1000000,
        likeCount: 50000,
        description: 'Sample video description',
        uploader: 'Sample Channel',
        uploadDate: DateTime.now().subtract(const Duration(days: 7)),
      );
    } catch (e) {
      throw ExtractionException(reason: 'Failed to extract YouTube video: $e');
    }
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    final videoInfo = await extractVideoInfo(url);
    
    return AudioInfo(
      id: videoInfo.id,
      title: videoInfo.title,
      artist: videoInfo.uploader,
      duration: videoInfo.duration,
      thumbnailUrl: videoInfo.thumbnailUrl,
      sourceUrl: url,
      platform: 'YouTube',
      availableFormats: videoInfo.audioQualities.map((aq) => AudioFormat(
        format: aq.format,
        downloadUrl: aq.downloadUrl,
        fileSize: aq.fileSize,
        bitrate: aq.bitrate,
        sampleRate: aq.sampleRate ?? 44100,
        channels: aq.channels ?? 2,
        isLossless: aq.format.toLowerCase() == 'flac',
        qualityLevel: _getQualityLevel(aq.bitrate),
      )).toList(),
      extractedAt: DateTime.now(),
    );
  }

  String? _extractVideoId(String url) {
    final patterns = [
      RegExp(r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})'),
      RegExp(r'youtube\.com\/embed\/([a-zA-Z0-9_-]{11})'),
    ];
    
    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null) {
        return match.group(1);
      }
    }
    
    return null;
  }

  AudioQualityLevel _getQualityLevel(int bitrate) {
    if (bitrate >= 320) return AudioQualityLevel.high;
    if (bitrate >= 192) return AudioQualityLevel.medium;
    return AudioQualityLevel.low;
  }
}

/// 🎵 TikTok Extractor
class TikTokExtractor extends PlatformExtractor {
  TikTokExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    // Mock implementation
    return VideoInfo(
      id: 'tiktok_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Sample TikTok Video',
      url: url,
      thumbnailUrl: 'https://via.placeholder.com/480x640/FF0050/FFFFFF?text=TikTok',
      platform: 'TikTok',
      duration: const Duration(seconds: 30),
      qualities: [
        const VideoQuality(
          quality: '720p',
          format: 'mp4',
          downloadUrl: 'https://example.com/tiktok.mp4',
          fileSize: 10 * 1024 * 1024,
          resolution: 720,
        ),
      ],
      audioQualities: [
        const AudioQuality(
          format: 'mp3',
          downloadUrl: 'https://example.com/tiktok_audio.mp3',
          fileSize: 1 * 1024 * 1024,
          bitrate: 128,
          sampleRate: 44100,
          channels: 2,
        ),
      ],
      hasWatermark: true,
    );
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    final videoInfo = await extractVideoInfo(url);
    
    return AudioInfo(
      id: videoInfo.id,
      title: videoInfo.title,
      duration: videoInfo.duration,
      thumbnailUrl: videoInfo.thumbnailUrl,
      sourceUrl: url,
      platform: 'TikTok',
      availableFormats: videoInfo.audioQualities.map((aq) => AudioFormat(
        format: aq.format,
        downloadUrl: aq.downloadUrl,
        fileSize: aq.fileSize,
        bitrate: aq.bitrate,
        sampleRate: aq.sampleRate ?? 44100,
        channels: aq.channels ?? 2,
        isLossless: false,
        qualityLevel: AudioQualityLevel.medium,
      )).toList(),
      extractedAt: DateTime.now(),
    );
  }
}

/// 📸 Instagram Extractor
class InstagramExtractor extends PlatformExtractor {
  InstagramExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    // Mock implementation
    return VideoInfo(
      id: 'instagram_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Instagram Video',
      url: url,
      thumbnailUrl: 'https://via.placeholder.com/640x640/E4405F/FFFFFF?text=Instagram',
      platform: 'Instagram',
      duration: const Duration(minutes: 1),
      qualities: [
        const VideoQuality(
          quality: '720p',
          format: 'mp4',
          downloadUrl: 'https://example.com/instagram.mp4',
          fileSize: 15 * 1024 * 1024,
          resolution: 720,
        ),
      ],
      audioQualities: [
        const AudioQuality(
          format: 'mp3',
          downloadUrl: 'https://example.com/instagram_audio.mp3',
          fileSize: 2 * 1024 * 1024,
          bitrate: 128,
          sampleRate: 44100,
          channels: 2,
        ),
      ],
    );
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    final videoInfo = await extractVideoInfo(url);
    
    return AudioInfo(
      id: videoInfo.id,
      title: videoInfo.title,
      duration: videoInfo.duration,
      thumbnailUrl: videoInfo.thumbnailUrl,
      sourceUrl: url,
      platform: 'Instagram',
      availableFormats: videoInfo.audioQualities.map((aq) => AudioFormat(
        format: aq.format,
        downloadUrl: aq.downloadUrl,
        fileSize: aq.fileSize,
        bitrate: aq.bitrate,
        sampleRate: aq.sampleRate ?? 44100,
        channels: aq.channels ?? 2,
        isLossless: false,
        qualityLevel: AudioQualityLevel.medium,
      )).toList(),
      extractedAt: DateTime.now(),
    );
  }
}

/// 📘 Facebook Extractor
class FacebookExtractor extends PlatformExtractor {
  FacebookExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    throw UnimplementedError('Facebook extractor not implemented yet');
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    throw UnimplementedError('Facebook audio extractor not implemented yet');
  }
}

/// 🐦 Twitter Extractor
class TwitterExtractor extends PlatformExtractor {
  TwitterExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    throw UnimplementedError('Twitter extractor not implemented yet');
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    throw UnimplementedError('Twitter audio extractor not implemented yet');
  }
}

/// 🎬 Vimeo Extractor
class VimeoExtractor extends PlatformExtractor {
  VimeoExtractor(super.dio);

  @override
  Future<VideoInfo> extractVideoInfo(String url) async {
    throw UnimplementedError('Vimeo extractor not implemented yet');
  }

  @override
  Future<AudioInfo> extractAudioInfo(String url) async {
    throw UnimplementedError('Vimeo audio extractor not implemented yet');
  }
}
